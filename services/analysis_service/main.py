"""
分析服务主应用
"""

import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from shared.utils.logger import setup_logging, get_logger
from services.analysis_service.config import config
from services.analysis_service.api import (
    analyze_router,
    results_router,
    files_router,
    health_router,
)

# 设置日志
setup_logging(level=config.log_level, service_name=config.service_name)

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理器"""
    # 启动时执行
    logger.info(f"Starting {config.service_name} v{config.service_version}")
    logger.info(f"Service running on {config.host}:{config.port}")
    logger.info(f"ChromaDB connection: {config.chroma_host}:{config.chroma_port}")
    logger.info(f"LLM Provider: {config.llm_provider}")
    logger.info(f"Streaming enabled: {config.enable_streaming}")
    
    yield
    
    # 关闭时执行
    logger.info(f"Shutting down {config.service_name}")


# 创建FastAPI应用
app = FastAPI(
    title="Deep Risk RAG Analysis Service",
    description="分析服务 - 负责风险分析、LLM调用和交互式界面",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health_router)
app.include_router(analyze_router)
app.include_router(results_router)
app.include_router(files_router)


if __name__ == "__main__":
    # 运行服务
    uvicorn.run(
        "main:app",
        host=config.host,
        port=config.port,
        reload=config.debug,
        log_level=config.log_level.lower(),
    )
