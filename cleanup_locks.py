#!/usr/bin/env python3
"""
清理 Hugging Face 模型下载锁文件的脚本
"""

import os
import shutil
from pathlib import Path


def cleanup_huggingface_locks():
    """清理 Hugging Face 缓存锁文件"""
    
    locks_dir = Path(".locks")
    
    if not locks_dir.exists():
        print("❌ .locks 文件夹不存在")
        return
    
    print("🔍 检查 .locks 文件夹...")
    
    total_files = 0
    total_size = 0
    
    # 统计锁文件
    for root, dirs, files in os.walk(locks_dir):
        for file in files:
            if file.endswith('.lock'):
                file_path = Path(root) / file
                total_files += 1
                total_size += file_path.stat().st_size
    
    print(f"📊 发现 {total_files} 个锁文件，总大小: {total_size} 字节")
    
    if total_files == 0:
        print("✅ 没有需要清理的锁文件")
        return
    
    # 询问用户是否清理
    response = input(f"是否删除这 {total_files} 个锁文件？(y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        try:
            shutil.rmtree(locks_dir)
            print("✅ 锁文件已全部清理")
        except Exception as e:
            print(f"❌ 清理失败: {e}")
    else:
        print("⏹️ 已取消清理")


def check_model_status():
    """检查模型状态，确认是否可以安全清理"""
    
    print("\n🔍 检查模型使用状态...")
    
    # 检查是否有Python进程在使用模型
    try:
        import psutil
        python_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and any('bge-m3' in str(cmd).lower() or 'huggingface' in str(cmd).lower() for cmd in cmdline):
                        python_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if python_processes:
            print("⚠️ 检测到可能正在使用模型的Python进程:")
            for proc in python_processes:
                print(f"  PID {proc['pid']}: {' '.join(proc['cmdline'][:3])}")
            print("建议先停止这些进程再清理锁文件")
        else:
            print("✅ 未检测到正在使用模型的进程")
            
    except ImportError:
        print("💡 提示: 安装 psutil 可以检查进程状态: pip install psutil")


if __name__ == "__main__":
    print("🧹 Hugging Face 锁文件清理工具")
    print("=" * 50)
    
    check_model_status()
    cleanup_huggingface_locks()
    
    print("\n💡 提示:")
    print("- 锁文件会在下次使用模型时自动重新创建")
    print("- 如果模型加载出现问题，可以尝试重新下载模型")
    print("- 运行: python prepare_model.py") 