"""
风险分析API路由
"""

import asyncio
import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse, StreamingResponse
import json

from shared.utils.logger import get_logger
from shared.llm import create_llm_from_settings
from services.analysis_service.models.requests import (
    AnalysisRequest,
    BatchAnalysisRequest,
    AnalyzeRequest,
)
from services.analysis_service.models.responses import (
    AnalysisResponse,
    BatchAnalysisResponse,
    AnalyzeResponse,
)
from services.analysis_service.core.risk_analyzer import RiskAnalyzer
from shared.models.analysis_result import AnalysisStatus
from services.analysis_service.config import config

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/analyze", tags=["analyze"])

# 全局实例
risk_analyzer = RiskAnalyzer()


async def get_llm():
    """获取LLM实例的依赖注入"""
    try:
        return create_llm_from_settings(
            settings=config,
            instance_key="analysis_llm",
            langchain_compatible=True  # Analysis服务需要LangChain兼容性
        )
    except Exception as e:
        logger.error(f"创建LLM实例失败: {e}")
        raise HTTPException(status_code=500, detail=f"LLM服务不可用: {str(e)}")


async def analyze_background(
    file_code: str, analysis_request: AnalysisRequest, analysis_id: str
):
    """
    后台分析任务

    Args:
        file_code: 文件编码
        analysis_request: 分析请求
        analysis_id: 分析ID
    """
    try:
        logger.info(f"开始后台分析任务: {file_code} (analysis_id: {analysis_id})")

        # 执行分析
        result = risk_analyzer.analyze_risk(
            file_code=file_code,
            analysis_type=analysis_request.analysis_type,
            options=analysis_request.options,
        )

        logger.info(f"后台分析任务完成: {file_code}")

    except Exception as e:
        logger.error(f"后台分析任务失败: {e}")


@router.post("/analyze", response_model=AnalyzeResponse)
async def analyze_risk(
    request: AnalyzeRequest,
    llm=Depends(get_llm)
):
    """
    执行风险分析
    """
    try:
        logger.info(f"开始风险分析: file_code={request.file_code}, type={request.analysis_type}")
        
        # 创建风险分析器
        analyzer = RiskAnalyzer(llm=llm)
        
        # 执行分析
        result = await analyzer.analyze(
            file_code=request.file_code,
            analysis_type=request.analysis_type,
            streaming=False
        )
        
        logger.info(f"风险分析完成: file_code={request.file_code}")
        return AnalyzeResponse(
            success=True,
            file_code=request.file_code,
            analysis_result=result
        )
        
    except Exception as e:
        logger.error(f"风险分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze/stream")
async def analyze_risk_stream(
    request: AnalyzeRequest,
    llm=Depends(get_llm)
):
    """
    流式风险分析
    """
    try:
        logger.info(f"开始流式风险分析: file_code={request.file_code}")
        
        # 创建风险分析器
        analyzer = RiskAnalyzer(llm=llm)
        
        async def generate_stream():
            try:
                async for chunk in analyzer.analyze_stream(
                    file_code=request.file_code,
                    analysis_type=request.analysis_type
                ):
                    # 构建SSE格式
                    data = {
                        "type": "chunk",
                        "content": chunk
                    }
                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                
                # 发送完成信号
                final_data = {
                    "type": "done",
                    "content": "分析完成"
                }
                yield f"data: {json.dumps(final_data, ensure_ascii=False)}\n\n"
                
            except Exception as e:
                # 发送错误信号
                error_data = {
                    "type": "error",
                    "content": str(e)
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        logger.error(f"流式风险分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{file_code}", response_model=AnalysisResponse)
async def start_analysis(
    file_code: str, analysis_request: AnalysisRequest, background_tasks: BackgroundTasks
):
    """
    开始风险分析

    Args:
        file_code: 文件编码
        analysis_request: 分析请求
        background_tasks: 后台任务

    Returns:
        分析响应
    """
    try:
        logger.info(f"收到分析请求: {file_code}")

        # 检查文件是否存在
        file_info = risk_analyzer.chroma_retriever.get_file_info(file_code)
        if not file_info:
            raise HTTPException(
                status_code=404, detail=f"File {file_code} not found in vector database"
            )

        # 执行同步分析（简化实现）
        result = risk_analyzer.analyze_risk(
            file_code=file_code,
            analysis_type=analysis_request.analysis_type,
            options=analysis_request.options,
        )

        return AnalysisResponse(
            success=True,
            message="Analysis completed",
            analysis_id=result.analysis_id,
            file_code=file_code,
            status=result.status,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析请求失败: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.post("/batch", response_model=BatchAnalysisResponse)
async def batch_analysis(
    batch_request: BatchAnalysisRequest, background_tasks: BackgroundTasks
):
    """
    批量分析

    Args:
        batch_request: 批量分析请求
        background_tasks: 后台任务

    Returns:
        批量分析响应
    """
    try:
        logger.info(f"收到批量分析请求: {len(batch_request.file_codes)} 个文件")

        # 检查所有文件是否存在
        missing_files = []
        for file_code in batch_request.file_codes:
            file_info = risk_analyzer.chroma_retriever.get_file_info(file_code)
            if not file_info:
                missing_files.append(file_code)

        if missing_files:
            raise HTTPException(
                status_code=404, detail=f"Files not found: {', '.join(missing_files)}"
            )

        # 执行批量分析
        analysis_ids = []
        for file_code in batch_request.file_codes:
            result = risk_analyzer.analyze_risk(
                file_code=file_code,
                analysis_type=batch_request.analysis_type,
                options=batch_request.options,
            )
            analysis_ids.append(result.analysis_id)

        batch_id = f"batch_{len(analysis_ids)}_{analysis_ids[0][:8]}"

        return BatchAnalysisResponse(
            success=True,
            message=f"Batch analysis completed for {len(analysis_ids)} files",
            batch_id=batch_id,
            analysis_ids=analysis_ids,
            total_count=len(analysis_ids),
            status="completed",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")


@router.get("/{file_code}/quick")
async def quick_analysis(file_code: str):
    """
    快速分析（简化版本）

    Args:
        file_code: 文件编码

    Returns:
        快速分析结果
    """
    try:
        # 检查文件是否存在
        file_info = risk_analyzer.chroma_retriever.get_file_info(file_code)
        if not file_info:
            raise HTTPException(status_code=404, detail=f"File {file_code} not found")

        # 执行快速分析
        result = risk_analyzer.analyze_risk(file_code=file_code)

        # 返回简化结果
        return {
            "success": True,
            "file_code": file_code,
            "analysis_id": result.analysis_id,
            "default_probability": result.default_probability,
            "risk_level": result.risk_level,
            "analysis_summary": result.analysis_summary,
            "processing_time": result.processing_time,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"快速分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"Quick analysis failed: {str(e)}")
