"""
ChromaDB检索器
直接连接ChromaDB进行文档检索
"""

from typing import List, Dict, Any, Optional
from shared.protocols.chroma_client import ChromaDBClient
from shared.utils.logger import get_logger
from services.analysis_service.config import config

logger = get_logger(__name__)


class ChromaRetriever:
    """ChromaDB检索器"""

    def __init__(self, chroma_host: str = None, chroma_port: int = None):
        """
        初始化ChromaDB检索器

        Args:
            chroma_host: ChromaDB主机
            chroma_port: ChromaDB端口
        """
        self.chroma_host = chroma_host or config.chroma_host
        self.chroma_port = chroma_port or config.chroma_port

        # 初始化ChromaDB客户端
        self.chroma_client = ChromaDBClient(
            host=self.chroma_host, port=self.chroma_port
        )

        logger.info(
            f"ChromaDB retriever initialized: {self.chroma_host}:{self.chroma_port}"
        )

    def retrieve_documents(
        self,
        file_code: str,
        query: str,
        top_k: int = None,
        similarity_threshold: float = None,
    ) -> List[Dict[str, Any]]:
        """
        检索相关文档

        Args:
            file_code: 文件编码
            query: 查询文本
            top_k: 返回结果数量
            similarity_threshold: 相似度阈值

        Returns:
            相关文档列表
        """
        top_k = top_k or config.retrieval_top_k
        similarity_threshold = similarity_threshold or config.similarity_threshold

        try:
            # 检查集合是否存在
            if not self.chroma_client.collection_exists(file_code):
                logger.warning(f"Collection not found for file_code: {file_code}")
                return []

            # 执行搜索
            results = self.chroma_client.search_documents(
                file_code=file_code, query_texts=[query], n_results=top_k
            )

            # 处理结果
            documents = []
            if results.get("documents") and len(results["documents"]) > 0:
                docs = results["documents"][0]  # 第一个查询的结果
                metadatas = results.get("metadatas", [[]])[0]
                distances = results.get("distances", [[]])[0]
                ids = results.get("ids", [[]])[0]

                for i, doc in enumerate(docs):
                    # 计算相似度分数 (1 - distance)
                    similarity = 1 - distances[i] if i < len(distances) else 0

                    # 应用相似度阈值
                    if similarity >= similarity_threshold:
                        documents.append(
                            {
                                "content": doc,
                                "metadata": metadatas[i] if i < len(metadatas) else {},
                                "similarity": similarity,
                                "id": ids[i] if i < len(ids) else f"doc_{i}",
                            }
                        )

            logger.info(
                f"Retrieved {len(documents)} documents for {file_code} (query: {query[:50]}...)"
            )
            return documents

        except Exception as e:
            logger.error(f"Failed to retrieve documents for {file_code}: {e}")
            return []

    def get_file_info(self, file_code: str) -> Optional[Dict[str, Any]]:
        """
        获取文件信息

        Args:
            file_code: 文件编码

        Returns:
            文件信息
        """
        try:
            if not self.chroma_client.collection_exists(file_code):
                return None

            document_count = self.chroma_client.get_collection_count(file_code)
            collection_name = self.chroma_client.get_collection_name(file_code)

            return {
                "file_code": file_code,
                "collection_name": collection_name,
                "document_count": document_count,
                "exists": True,
            }

        except Exception as e:
            logger.error(f"Failed to get file info for {file_code}: {e}")
            return None

    def list_available_files(self) -> List[str]:
        """
        列出所有可用的文件编码

        Returns:
            文件编码列表
        """
        try:
            collections = self.chroma_client.list_collections()

            # 提取文件编码（去掉前缀 "risk_analysis_"）
            file_codes = []
            for collection_name in collections:
                if collection_name.startswith("risk_analysis_"):
                    file_code = collection_name[len("risk_analysis_") :]
                    file_codes.append(file_code)

            logger.info(f"Found {len(file_codes)} available files")
            return file_codes

        except Exception as e:
            logger.error(f"Failed to list available files: {e}")
            return []

    def health_check(self) -> bool:
        """
        健康检查

        Returns:
            服务是否健康
        """
        return self.chroma_client.health_check()
