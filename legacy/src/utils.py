"""
通用工具模块 - 消除重复代码，提供统一的工具函数
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional, Any, Dict
from functools import lru_cache


# 项目根目录（缓存结果避免重复计算）
@lru_cache(maxsize=1)
def get_project_root() -> Path:
    """获取项目根目录"""
    current_file = Path(__file__).resolve()
    # 从 src/utils.py 向上两级到项目根目录
    return current_file.parent.parent


def setup_project_path():
    """设置项目路径到 sys.path"""
    project_root = get_project_root()
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))


def get_logger(name: str, level: str = "INFO") -> logging.Logger:
    """
    获取统一配置的日志器

    Args:
        name: 日志器名称
        level: 日志级别

    Returns:
        配置好的日志器
    """
    logger = logging.getLogger(name)

    # 避免重复配置
    if not logger.handlers:
        # 设置日志级别
        log_level = getattr(logging, level.upper(), logging.INFO)
        logger.setLevel(log_level)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)

        # 设置格式
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        console_handler.setFormatter(formatter)

        # 添加处理器
        logger.addHandler(console_handler)

        # 防止日志传播到根日志器
        logger.propagate = False

    return logger


@lru_cache(maxsize=1)
def load_settings():
    """
    加载配置设置（缓存结果）

    Returns:
        配置对象或默认配置
    """
    setup_project_path()

    try:
        from config import settings

        return settings
    except ImportError:
        # 返回默认配置
        return create_default_settings()


def create_default_settings():
    """创建默认配置对象"""

    class DefaultSettings:
        # 项目路径
        PROJECT_ROOT = get_project_root()
        DATA_DIR = PROJECT_ROOT / "data"
        DOCUMENTS_DIR = DATA_DIR / "documents"
        CACHE_DIR = PROJECT_ROOT / "cache"
        MODELS_CACHE_DIR = CACHE_DIR / "models"

        # DeepSeek API 配置
        DEEPSEEK_API_KEY = None
        DEEPSEEK_BASE_URL = "https://api.deepseek.com"
        DEEPSEEK_MODEL = "deepseek-chat"

        # 嵌入模型配置
        EMBEDDING_MODEL_NAME = "BAAI/bge-m3"
        EMBEDDING_MODEL_CACHE_DIR = None
        EMBEDDING_BATCH_SIZE = 32
        EMBEDDING_MAX_LENGTH = 8192

        # 向量数据库配置
        VECTOR_DB_TYPE = "chroma"
        CHROMA_PERSIST_DIR = "./cache/chroma_db"
        CHROMA_COLLECTION_NAME = "deep_risk_documents"

        # 文档处理配置
        CHUNK_SIZE = 1000
        CHUNK_OVERLAP = 200
        MAX_DOCUMENT_SIZE_MB = 50

        # 检索配置
        RETRIEVAL_TOP_K = 5
        SIMILARITY_THRESHOLD = 0.7

        # RAG 配置
        ENABLE_DOCUMENT_RETRIEVAL = True  # 是否启用文档检索
        MAX_CONTEXT_LENGTH = 32000  # 最大上下文长度
        TEMPERATURE = 0.1
        MAX_TOKENS = 2000

        # 系统配置
        LOG_LEVEL = "INFO"
        ENABLE_CACHE = True
        CACHE_TTL_SECONDS = 3600

        # ChromaDB 配置
        ANONYMIZED_TELEMETRY = False

    return DefaultSettings()


def safe_import(module_name: str, package: Optional[str] = None) -> Optional[Any]:
    """
    安全导入模块，失败时返回 None

    Args:
        module_name: 模块名称
        package: 包名称

    Returns:
        导入的模块或 None
    """
    try:
        if package:
            return __import__(module_name, fromlist=[package])
        else:
            return __import__(module_name)
    except ImportError:
        return None


def ensure_directory(path: Path) -> bool:
    """
    确保目录存在

    Args:
        path: 目录路径

    Returns:
        是否成功创建或已存在
    """
    try:
        path.mkdir(parents=True, exist_ok=True)
        return True
    except Exception:
        return False


def get_file_size_mb(file_path: Path) -> float:
    """
    获取文件大小（MB）

    Args:
        file_path: 文件路径

    Returns:
        文件大小（MB）
    """
    try:
        return file_path.stat().st_size / (1024 * 1024)
    except Exception:
        return 0.0


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """
    截断文本

    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 后缀

    Returns:
        截断后的文本
    """
    if len(text) <= max_length:
        return text
    return text[: max_length - len(suffix)] + suffix


def batch_process(items: list, batch_size: int):
    """
    批量处理生成器

    Args:
        items: 要处理的项目列表
        batch_size: 批次大小

    Yields:
        批次数据
    """
    for i in range(0, len(items), batch_size):
        yield items[i : i + batch_size]


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小

    Args:
        size_bytes: 字节数

    Returns:
        格式化的大小字符串
    """
    for unit in ["B", "KB", "MB", "GB"]:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"


def validate_api_key(api_key: Optional[str]) -> bool:
    """
    验证 API 密钥

    Args:
        api_key: API 密钥

    Returns:
        是否有效
    """
    return api_key is not None and len(api_key.strip()) > 0


def get_env_var(key: str, default: Any = None) -> Any:
    """
    获取环境变量

    Args:
        key: 环境变量键
        default: 默认值

    Returns:
        环境变量值或默认值
    """
    return os.getenv(key, default)


class PerformanceTimer:
    """性能计时器上下文管理器"""

    def __init__(self, name: str, logger: Optional[logging.Logger] = None):
        self.name = name
        self.logger = logger or get_logger(__name__)
        self.start_time = None

    def __enter__(self):
        import time

        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        import time

        elapsed = time.time() - self.start_time
        self.logger.info(f"⏱️  {self.name} 耗时: {elapsed:.2f} 秒")


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """
    重试装饰器

    Args:
        max_retries: 最大重试次数
        delay: 重试延迟（秒）
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            import time

            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(delay * (2**attempt))  # 指数退避
                    else:
                        raise last_exception
            return None

        return wrapper

    return decorator


# 常用的错误处理函数
def handle_import_error(module_name: str, install_command: str = None) -> None:
    """
    处理导入错误

    Args:
        module_name: 模块名称
        install_command: 安装命令
    """
    logger = get_logger(__name__)
    logger.error(f"❌ 缺少必要的模块: {module_name}")

    if install_command:
        logger.error(f"请运行: {install_command}")
    else:
        logger.error("请确保已安装所有依赖: pip install -r requirements.txt")


def handle_file_error(file_path: Path, operation: str) -> None:
    """
    处理文件操作错误

    Args:
        file_path: 文件路径
        operation: 操作类型
    """
    logger = get_logger(__name__)
    logger.error(f"❌ 文件{operation}失败: {file_path}")


# 导出常用函数
__all__ = [
    "get_project_root",
    "setup_project_path",
    "get_logger",
    "load_settings",
    "safe_import",
    "ensure_directory",
    "get_file_size_mb",
    "truncate_text",
    "batch_process",
    "format_file_size",
    "validate_api_key",
    "get_env_var",
    "PerformanceTimer",
    "retry_on_failure",
    "handle_import_error",
    "handle_file_error",
]
