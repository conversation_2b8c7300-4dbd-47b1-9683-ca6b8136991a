# Worker Service 验证脚本使用说明

本文档介绍如何使用Deep Risk RAG项目的Worker Service验证脚本。

## 概述

Worker验证脚本用于测试和验证Celery任务队列的功能和性能，确保worker服务的各个组件正常运行。

## 可用脚本

### 1. worker_validation.py (完整版)
- **功能**: 提供丰富的交互界面和可视化
- **依赖**: rich, psutil, celery, redis
- **特点**: 美观的CLI界面、实时进度显示、详细的性能统计

### 2. worker_validation_simple.py (简化版)
- **功能**: 基础验证功能，无额外UI依赖
- **依赖**: celery, redis (psutil可选)
- **特点**: 简洁的文本输出、适合所有环境

## 安装依赖

### 必需依赖
```bash
pip install celery redis
```

### 可选依赖（用于完整版）
```bash
pip install rich psutil
```

### 项目依赖
确保项目的其他依赖已安装：
```bash
pip install -r requirements.txt
```

## 使用方法

### 运行完整版验证脚本
```bash
# 进入项目根目录
cd /path/to/deep-risk-rag

# 运行完整版脚本
python scripts/worker_validation.py
```

### 运行简化版验证脚本
```bash
# 进入项目根目录
cd /path/to/deep-risk-rag

# 运行简化版脚本
python scripts/worker_validation_simple.py
```

### 直接执行（如果已设置执行权限）
```bash
./scripts/worker_validation.py
./scripts/worker_validation_simple.py
```

## 验证模式

### 1. 完整验证 (推荐)
- 连接测试：Redis、ChromaDB、Celery
- 任务测试：健康检查、向量化、风险分析
- 性能监控：系统资源使用情况
- 详细报告：成功率统计和问题诊断

### 2. 仅连接测试
- 快速检查各服务的连接状态
- 适合初步诊断网络和配置问题

### 3. 仅任务测试
- 跳过连接检查，直接测试任务功能
- 适合已知连接正常的情况

## 测试项目说明

### 连接测试

#### Redis连接测试
- **目的**: 验证Redis服务器连接和配置
- **检查项**: 连接状态、版本信息
- **依赖**: Redis服务器运行、网络连通

#### ChromaDB连接测试
- **目的**: 验证向量数据库连接
- **检查项**: HTTP心跳检查、服务状态
- **依赖**: ChromaDB服务运行、API可访问

#### Celery连接测试
- **目的**: 验证任务队列系统
- **检查项**: Worker数量、连接状态
- **依赖**: Celery Worker运行、Redis连接

### 任务测试

#### 健康检查任务
- **目的**: 验证Worker的基础功能
- **测试内容**: 系统资源、GPU状态、组件健康
- **超时时间**: 30秒
- **预期结果**: Worker服务状态报告

#### 向量化任务
- **目的**: 验证文档向量化流程
- **测试内容**: 文件处理、BGE-M3嵌入、ChromaDB存储
- **超时时间**: 60秒
- **预期结果**: 文档块数量、处理时间

#### 风险分析任务
- **目的**: 验证风险评估功能
- **测试内容**: 数据检索、LLM分析、结果生成
- **超时时间**: 120秒
- **预期结果**: 分析报告、处理时间

## 输出解读

### 成功率指标
- **80%以上**: 🎉 Worker服务运行状况良好
- **60-80%**: ⚠️ 存在一些问题，建议检查
- **60%以下**: ❌ 存在严重问题，需要立即处理

### 常见错误及解决方案

#### Redis连接失败
```
❌ Redis连接失败: Connection refused
```
**解决方案**:
1. 检查Redis服务是否运行
2. 验证连接配置（host、port、password）
3. 检查网络连通性

#### ChromaDB连接失败
```
❌ ChromaDB连接失败: HTTP 404
```
**解决方案**:
1. 确认ChromaDB服务运行
2. 检查服务URL配置
3. 验证API端点可访问性

#### Worker未发现
```
❌ Celery连接失败: 未发现活跃的Worker
```
**解决方案**:
1. 启动Celery Worker
2. 检查队列配置
3. 验证Redis连接

#### 任务超时
```
❌ 向量化任务超时（60秒）
```
**解决方案**:
1. 检查Worker资源使用
2. 验证模型文件完整性
3. 增加超时时间或优化任务

## 环境要求

### 系统要求
- Python 3.8+
- 操作系统: Linux, macOS, Windows

### 服务依赖
- Redis服务器运行
- ChromaDB服务运行（可选）
- Celery Worker运行

### 配置文件
确保以下配置文件正确设置：
- `.env` - 环境变量配置
- `shared/redis_config.py` - Redis连接配置
- `services/worker_service/config.py` - Worker配置

## 故障排除

### 模块导入错误
```python
ImportError: No module named 'rich'
```
**解决方案**: 使用简化版脚本或安装缺失的依赖

### 权限错误
```bash
Permission denied: './scripts/worker_validation.py'
```
**解决方案**: 
```bash
chmod +x scripts/worker_validation.py
```

### 路径错误
```
❌ 请在项目根目录运行此脚本
```
**解决方案**: 确保在项目根目录执行脚本

## 高级选项

### 环境变量配置
可以通过环境变量覆盖默认配置：
```bash
export REDIS_HOST=localhost
export REDIS_PORT=6379
export CHROMA_HOST=localhost
export CHROMA_PORT=8001
```

### 批量测试
对于CI/CD环境，可以使用非交互模式：
```python
# 在脚本中直接调用验证方法
validator = SimpleWorkerValidator()
asyncio.run(validator.run_full_validation())
```

## 性能优化建议

1. **资源监控**: 关注CPU、内存使用率
2. **任务并发**: 根据系统资源调整Worker数量
3. **超时设置**: 根据实际性能调整任务超时时间
4. **缓存优化**: 确保模型文件缓存有效

## 联系支持

如果验证过程中遇到问题：
1. 查看日志文件获取详细错误信息
2. 检查相关服务的运行状态
3. 参考项目文档进行故障排除
4. 提交Issue到项目仓库 