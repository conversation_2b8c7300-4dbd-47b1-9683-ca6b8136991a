"""
健康检查API路由
"""

from fastapi import APIRouter
from services.analysis_service.models.responses import HealthResponse
from services.analysis_service.core.risk_analyzer import RiskAnalyzer
from shared.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(tags=["health"])

# 全局实例
risk_analyzer = RiskAnalyzer()


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    健康检查接口

    Returns:
        健康状态
    """
    try:
        # 执行各组件健康检查
        checks = risk_analyzer.health_check()

        # 判断整体健康状态
        all_healthy = all(checks.values())
        status = "healthy" if all_healthy else "unhealthy"

        return HealthResponse(
            status=status, service="analysis-service", version="1.0.0", checks=checks
        )

    except Exception as e:
        logger.error(f"Health check error: {e}")
        return HealthResponse(
            status="unhealthy",
            service="analysis-service",
            version="1.0.0",
            checks={"error": False},
        )


@router.get("/")
async def root():
    """
    根路径

    Returns:
        服务信息
    """
    return {
        "service": "analysis-service",
        "version": "1.0.0",
        "description": "Deep Risk RAG Analysis Service",
        "endpoints": {
            "analyze": "/analyze/{file_code}",
            "results": "/result/{analysis_id}",
            "files": "/files/",
            "health": "/health",
            "docs": "/docs",
        },
    }
