"""
向量化服务主应用
"""

import os
import uvicorn
import asyncio
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path

# 完全禁用所有遥测数据发送，保护隐私和数据安全
os.environ["POSTHOG_HOST"] = ""
os.environ["POSTHOG_PROJECT_ID"] = ""
os.environ["POSTHOG_API_KEY"] = ""
os.environ["POSTHOG_FEATURE_FLAGS"] = "false"
os.environ["LANGCHAIN_TRACING_V2"] = "false"
os.environ["LANGCHAIN_ANALYTICS"] = "false"
os.environ["LANGCHAIN_TRACING"] = "false"
os.environ["LANGCHAIN_TRACKING"] = "false"
os.environ["LANGCHAIN_ENDPOINT"] = ""
os.environ["LANGCHAIN_API_KEY"] = ""
# 禁用其他可能的遥测
os.environ["TELEMETRY_DISABLED"] = "true"
os.environ["DO_NOT_TRACK"] = "1"
os.environ["ANALYTICS_DISABLED"] = "true"

from shared.utils.logger import setup_logging, get_logger
from services.vector_service.config import config
from services.vector_service.api import upload_router, status_router, health_router
from services.vector_service.core.embeddings import BGEEmbeddingService
from services.vector_service.core.vector_manager import VectorManager

# 设置日志
setup_logging(level=config.log_level, service_name=config.service_name)

logger = get_logger(__name__)

# 全局服务实例
_embedding_service = None
_vector_manager = None
_vector_executor = None


def get_embedding_service() -> BGEEmbeddingService:
    """获取嵌入服务实例"""
    global _embedding_service
    if _embedding_service is None:
        raise RuntimeError("Embedding service not initialized")
    return _embedding_service


def get_vector_manager() -> VectorManager:
    """获取向量管理器实例"""
    global _vector_manager
    if _vector_manager is None:
        raise RuntimeError("Vector manager not initialized")
    return _vector_manager


def get_vector_executor() -> ThreadPoolExecutor:
    """获取向量化线程池实例"""
    global _vector_executor
    if _vector_executor is None:
        raise RuntimeError("Vector executor not initialized")
    return _vector_executor


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理器"""
    global _embedding_service, _vector_manager, _vector_executor
    
    # 启动时执行
    logger.info("=" * 60)
    logger.info(f"🚀 启动 {config.service_name} v{config.service_version}")
    logger.info("=" * 60)
    
    try:
        # 1. 基础服务信息
        logger.info(f"📡 服务地址: {config.host}:{config.port}")
        logger.info(f"🐛 调试模式: {config.debug}")
        logger.info(f"📝 日志级别: {config.log_level}")
        
        # 2. 预检查和目录创建
        logger.info("🔄 执行启动前检查...")
        
        # ChromaDB 配置（本地模式）
        logger.info(f"💾 ChromaDB 本地目录: {config.chroma_persist_dir}")
        chroma_path = Path(config.chroma_persist_dir)
        chroma_path.mkdir(parents=True, exist_ok=True)
        logger.info("✅ ChromaDB 目录准备完成")
        
        # 上传目录
        upload_path = Path(config.upload_dir)
        upload_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"📂 上传目录: {config.upload_dir} ✅")
        
        # 模型缓存目录
        model_cache_path = Path(config.model_cache_dir)
        model_cache_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"💾 模型缓存目录: {config.model_cache_dir} ✅")
        
        # 3. 初始化向量化线程池
        logger.info("🔄 初始化向量化线程池...")
        _vector_executor = ThreadPoolExecutor(
            max_workers=1,  # 单线程避免CPU过载
            thread_name_prefix="vector_"
        )
        logger.info("✅ 向量化线程池初始化成功")
        
        # 4. 配置信息展示
        logger.info("📋 服务配置:")
        logger.info(f"   📊 最大文件大小: {config.max_file_size}MB")
        logger.info(f"   📄 支持文件类型: {', '.join(config.allowed_extensions)}")
        logger.info(f"   🤖 模型名称: {config.model_name}")
        logger.info(f"   ⚙️  设备: {config.device}")
        logger.info(f"   📦 批处理大小: {config.batch_size}")
        logger.info(f"   📏 最大序列长度: {config.max_length}")
        logger.info(f"   🧵 向量化线程数: 1 (CPU优化)")
        
        # 5. 初始化核心组件（启动时加载）
        logger.info("🔄 初始化嵌入服务...")
        _embedding_service = BGEEmbeddingService()
        logger.info("✅ 嵌入服务初始化成功")
        
        logger.info("🔄 初始化向量管理器...")
        _vector_manager = VectorManager(embedding_service=_embedding_service)
        logger.info("✅ 向量管理器初始化成功")
        
        # 6. 健康检查
        logger.info("🔄 执行系统健康检查...")
        health_result = _vector_manager.health_check()
        
        if all(health_result.values()):
            logger.info("✅ 系统健康检查通过")
            for component, status in health_result.items():
                logger.info(f"   - {component}: {'✅ 正常' if status else '❌ 异常'}")
        else:
            logger.warning("⚠️  系统健康检查部分失败")
            for component, status in health_result.items():
                logger.info(f"   - {component}: {'✅ 正常' if status else '❌ 异常'}")
        
        # 存储服务工厂函数供路由使用
        app.state.get_embedding_service = get_embedding_service
        app.state.get_vector_manager = get_vector_manager
        app.state.get_vector_executor = get_vector_executor
        
    except Exception as e:
        logger.error(f"❌ 服务初始化失败: {e}")
        logger.error("🔧 请检查：")
        logger.error("   1. 模型文件是否正确下载")
        logger.error("   2. 依赖包是否正确安装")
        logger.error("   3. 磁盘空间是否充足")
        logger.error("   4. 权限设置是否正确")
        raise
    
    logger.info("=" * 60)
    logger.info("🎉 向量化服务启动完成，所有组件就绪")
    logger.info("=" * 60)
    
    yield
    
    # 关闭时执行
    logger.info("=" * 60)
    logger.info(f"🛑 关闭 {config.service_name}")
    
    # 清理资源
    try:
        if _vector_executor is not None:
            logger.info("🔄 关闭向量化线程池...")
            _vector_executor.shutdown(wait=True, timeout=30)
            _vector_executor = None
            logger.info("✅ 向量化线程池关闭完成")
        
        if _embedding_service is not None:
            logger.info("🔄 清理嵌入服务资源...")
            # 如果嵌入服务有清理方法，调用它
            if hasattr(_embedding_service, 'cleanup'):
                _embedding_service.cleanup()
            _embedding_service = None
            logger.info("✅ 嵌入服务资源清理完成")
        
        if _vector_manager is not None:
            logger.info("🔄 清理向量管理器资源...")
            _vector_manager = None
            logger.info("✅ 向量管理器资源清理完成")
            
    except Exception as e:
        logger.warning(f"⚠️  资源清理时出现警告: {e}")
    
    logger.info("✅ 服务关闭完成")
    logger.info("=" * 60)


# 创建FastAPI应用
app = FastAPI(
    title="Deep Risk RAG Vector Service",
    description="向量化服务 - 负责文件上传、BGE-M3嵌入生成和ChromaDB存储",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health_router)
app.include_router(upload_router)
app.include_router(status_router)


if __name__ == "__main__":
    # 运行服务
    uvicorn.run(
        "main:app",
        host=config.host,
        port=config.port,
        reload=config.debug,
        log_level=config.log_level.lower(),
    )
