# 统一服务Dockerfile
# 合并了原有分析服务和向量化服务的功能

FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY shared/ ./shared/
COPY services/unified_service/ ./services/unified_service/
COPY prompts/ ./prompts/

# 创建必要的目录
RUN mkdir -p /app/data/uploads /app/logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8000}/health || exit 1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "services.unified_service.main:app", "--host", "0.0.0.0", "--port", "8000"]
