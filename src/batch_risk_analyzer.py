"""
多批次风险分析器
支持将大量文档分批处理，确保所有数据都参与风险分析
"""

import logging
from typing import List, Dict, Any, Optional
from langchain_core.documents import Document
from datetime import datetime
import json
import re

from .risk_analyzer import RiskAnalyzer
from .file_coded_vector_store import FileCodedVectorStore

logger = logging.getLogger(__name__)


class BatchRiskAnalyzer:
    """
    多批次风险分析器
    
    将大量文档分成多个批次进行分析，然后合并结果
    确保所有文档都参与风险评估，提供更全面的分析
    """
    
    def __init__(
        self,
        file_coded_vector_store: Optional[FileCodedVectorStore] = None,
        api_key: Optional[str] = None,
        model_name: str = "deepseek-chat",
        temperature: float = 0.1,
        max_tokens: int = None,
        batch_size: int = 20,  # 每批处理的文档数量
        max_batch_length: int = 60000,  # 每批的最大字符长度
    ):
        """
        初始化多批次风险分析器
        
        Args:
            file_coded_vector_store: 文件编码向量存储
            api_key: API密钥
            model_name: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            batch_size: 每批处理的文档数量
            max_batch_length: 每批的最大字符长度
        """
        # 初始化基础风险分析器
        self.base_analyzer = RiskAnalyzer(
            file_coded_vector_store=file_coded_vector_store,
            api_key=api_key,
            model_name=model_name,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        self.batch_size = batch_size
        self.max_batch_length = max_batch_length
        
        logger.info(f"✅ 多批次风险分析器初始化完成")
        logger.info(f"批次大小: {batch_size} 文档/批")
        logger.info(f"批次最大长度: {max_batch_length} 字符/批")
    
    def generate_risk_report_by_file_code(self, file_code: str) -> Dict[str, Any]:
        """
        使用多批次策略为指定文件编码生成风险报告
        
        Args:
            file_code: 文件编码
            
        Returns:
            风险报告字典
        """
        try:
            logger.info(f"开始多批次风险分析，文件编码: {file_code}")
            
            # 获取所有文档
            documents = self.base_analyzer.vector_store_manager.get_all_documents_by_code(file_code)
            if not documents:
                return {
                    "success": False,
                    "error": f"未找到文件编码 {file_code} 对应的文档"
                }
            
            logger.info(f"获取到 {len(documents)} 个文档，开始分批处理")
            
            # 分批处理文档
            batches = self._create_document_batches(documents)
            logger.info(f"分为 {len(batches)} 个批次进行处理")
            
            # 分析每个批次
            batch_results = []
            for i, batch in enumerate(batches, 1):
                logger.info(f"处理第 {i}/{len(batches)} 批次，包含 {len(batch)} 个文档")
                
                batch_result = self._analyze_document_batch(batch, i, len(batches))
                if batch_result:
                    batch_results.append(batch_result)
                    logger.info(f"第 {i} 批次分析完成")
                else:
                    logger.warning(f"第 {i} 批次分析失败")
            
            if not batch_results:
                return {
                    "success": False,
                    "error": "所有批次分析都失败了"
                }
            
            # 合并批次结果
            logger.info(f"开始合并 {len(batch_results)} 个批次的分析结果")
            final_result = self._merge_batch_results(batch_results, file_code)
            
            logger.info(f"✅ 多批次风险分析完成，最终违约概率: {final_result.get('default_probability', 'N/A')}")
            return final_result
            
        except Exception as e:
            logger.error(f"多批次风险分析失败: {e}")
            return {
                "success": False,
                "error": f"多批次风险分析失败: {str(e)}"
            }
    
    def _create_document_batches(self, documents: List[Document]) -> List[List[Document]]:
        """
        将文档分成多个批次
        
        Args:
            documents: 所有文档
            
        Returns:
            文档批次列表
        """
        batches = []
        current_batch = []
        current_length = 0
        
        for doc in documents:
            doc_length = len(doc.page_content)
            
            # 检查是否需要开始新批次
            if (len(current_batch) >= self.batch_size or 
                current_length + doc_length > self.max_batch_length):
                
                if current_batch:  # 如果当前批次不为空，保存它
                    batches.append(current_batch)
                    current_batch = []
                    current_length = 0
            
            current_batch.append(doc)
            current_length += doc_length
        
        # 添加最后一个批次
        if current_batch:
            batches.append(current_batch)
        
        return batches
    
    def _analyze_document_batch(self, batch: List[Document], batch_num: int, total_batches: int) -> Optional[Dict[str, Any]]:
        """
        分析单个文档批次
        
        Args:
            batch: 文档批次
            batch_num: 批次编号
            total_batches: 总批次数
            
        Returns:
            批次分析结果
        """
        try:
            # 提取批次数据
            batch_data = self._extract_batch_data(batch, batch_num, total_batches)
            
            # 使用专门的批次分析提示词
            batch_prompt = self._create_batch_analysis_prompt(batch_data, batch_num, total_batches)
            
            # 调用AI分析（使用基础分析器的流式功能）
            if self.base_analyzer.enable_streaming:
                logger.info(f"批次 {batch_num} 使用流式分析")
                analysis_content = self.base_analyzer._stream_analysis(batch_prompt)
            else:
                analysis_content = self.base_analyzer._non_stream_analysis(batch_prompt)

            # 解析批次结果
            batch_result = self._parse_batch_response(analysis_content, batch_num)
            
            return batch_result
            
        except Exception as e:
            logger.error(f"批次 {batch_num} 分析失败: {e}")
            return None
    
    def _extract_batch_data(self, batch: List[Document], batch_num: int, total_batches: int) -> str:
        """
        从批次文档中提取数据
        
        Args:
            batch: 文档批次
            batch_num: 批次编号
            total_batches: 总批次数
            
        Returns:
            格式化的批次数据
        """
        batch_parts = []
        
        for i, doc in enumerate(batch, 1):
            file_name = doc.metadata.get("file_name", "未知文件")
            chunk_id = doc.metadata.get("chunk_id", "")
            sheet_name = doc.metadata.get("sheet_name", doc.metadata.get("original_sheet", ""))
            
            content = doc.page_content.strip()
            
            chunk_info = f" (块 {chunk_id})" if chunk_id != "" else ""
            sheet_info = f" - {sheet_name}" if sheet_name else ""
            
            data_part = f"""
                文档 {i}{chunk_info}{sheet_info}:
                文件: {file_name}
                内容: {content}
            ---"""
            
            batch_parts.append(data_part)
        
        batch_header = f"""
=== 批次 {batch_num}/{total_batches} 风控数据 ===
本批次包含 {len(batch)} 个文档
"""
        
        return batch_header + "\n".join(batch_parts)
    
    def _create_batch_analysis_prompt(self, batch_data: str, batch_num: int, total_batches: int) -> str:
        """
        创建批次分析的提示词
        
        Args:
            batch_data: 批次数据
            batch_num: 批次编号
            total_batches: 总批次数
            
        Returns:
            批次分析提示词
        """
        return f"""
你是一位专业的风险分析师，正在对客户的风控数据进行分批分析。

当前任务：分析第 {batch_num}/{total_batches} 批次的数据

分析要求：
1. 仔细分析本批次中的所有风控数据
2. 识别关键的风险指标和特征
3. 评估本批次数据反映的风险水平
4. 提供本批次的风险评估结论

风控数据：
{batch_data}

请按以下JSON格式返回分析结果：
{{
    "batch_number": {batch_num},
    "documents_analyzed": "本批次分析的文档数量",
    "key_risk_factors": ["识别到的关键风险因素列表"],
    "risk_indicators": {{
        "income_stability": "收入稳定性评估",
        "debt_level": "负债水平评估", 
        "credit_history": "信用历史评估",
        "repayment_ability": "还款能力评估"
    }},
    "batch_risk_score": "本批次风险评分(0-100)",
    "batch_risk_level": "本批次风险等级(低风险/中风险/高风险)",
    "key_findings": "本批次的关键发现",
    "concerns": ["本批次发现的主要风险点"]
}}

注意：这是多批次分析的一部分，请专注于本批次数据的分析。
"""

    def _parse_batch_response(self, response: str, batch_num: int) -> Dict[str, Any]:
        """
        解析批次分析响应

        Args:
            response: AI响应
            batch_num: 批次编号

        Returns:
            解析后的批次结果
        """
        try:
            # 尝试提取JSON
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                result['batch_number'] = batch_num
                result['raw_response'] = response
                return result
            else:
                # 如果没有找到JSON，创建基础结构
                logger.warning(f"批次 {batch_num} 响应格式不正确，使用默认解析")
                return {
                    'batch_number': batch_num,
                    'documents_analyzed': 'unknown',
                    'key_risk_factors': [],
                    'risk_indicators': {},
                    'batch_risk_score': 50,
                    'batch_risk_level': '中风险',
                    'key_findings': response[:500],
                    'concerns': [],
                    'raw_response': response
                }
        except Exception as e:
            logger.error(f"解析批次 {batch_num} 响应失败: {e}")
            return {
                'batch_number': batch_num,
                'error': str(e),
                'raw_response': response
            }

    def _merge_batch_results(self, batch_results: List[Dict[str, Any]], file_code: str) -> Dict[str, Any]:
        """
        合并所有批次的分析结果

        Args:
            batch_results: 所有批次的分析结果
            file_code: 文件编码

        Returns:
            最终合并的风险报告
        """
        try:
            # 收集所有批次的关键信息
            all_risk_factors = []
            all_concerns = []
            risk_scores = []
            risk_levels = []
            key_findings = []

            for result in batch_results:
                if 'error' not in result:
                    all_risk_factors.extend(result.get('key_risk_factors', []))
                    all_concerns.extend(result.get('concerns', []))

                    if 'batch_risk_score' in result:
                        try:
                            score = float(result['batch_risk_score'])
                            risk_scores.append(score)
                        except (ValueError, TypeError):
                            pass

                    risk_levels.append(result.get('batch_risk_level', ''))
                    key_findings.append(result.get('key_findings', ''))

            # 计算综合风险评分
            if risk_scores:
                avg_risk_score = sum(risk_scores) / len(risk_scores)
                # 考虑批次间的一致性，如果分歧较大则提高风险
                score_variance = max(risk_scores) - min(risk_scores)
                if score_variance > 30:  # 如果批次间分歧较大
                    avg_risk_score += 10  # 增加风险评分
                avg_risk_score = min(100, max(0, avg_risk_score))  # 限制在0-100范围内
            else:
                avg_risk_score = 50  # 默认中等风险

            # 确定最终风险等级
            if avg_risk_score >= 70:
                final_risk_level = "高风险"
                default_probability = 0.6 + (avg_risk_score - 70) * 0.01  # 60%-90%
            elif avg_risk_score >= 40:
                final_risk_level = "中风险"
                default_probability = 0.2 + (avg_risk_score - 40) * 0.013  # 20%-60%
            else:
                final_risk_level = "低风险"
                default_probability = avg_risk_score * 0.005  # 0%-20%

            default_probability = min(0.9, max(0.01, default_probability))

            # 生成综合分析报告
            comprehensive_analysis = self._generate_comprehensive_analysis(
                batch_results, avg_risk_score, final_risk_level, default_probability
            )

            return {
                "success": True,
                "file_code": file_code,
                "analysis_method": "multi_batch",
                "total_batches": len(batch_results),
                "risk_score": round(avg_risk_score, 2),
                "risk_level": final_risk_level,
                "default_probability": round(default_probability, 4),
                "key_risk_factors": list(set(all_risk_factors)),  # 去重
                "main_concerns": list(set(all_concerns)),  # 去重
                "comprehensive_analysis": comprehensive_analysis,
                "batch_details": batch_results,
                "analysis_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"合并批次结果失败: {e}")
            return {
                "success": False,
                "error": f"合并批次结果失败: {str(e)}",
                "batch_results": batch_results
            }

    def _generate_comprehensive_analysis(
        self,
        batch_results: List[Dict[str, Any]],
        avg_risk_score: float,
        final_risk_level: str,
        default_probability: float
    ) -> str:
        """
        生成综合分析报告

        Args:
            batch_results: 批次结果列表
            avg_risk_score: 平均风险评分
            final_risk_level: 最终风险等级
            default_probability: 违约概率

        Returns:
            综合分析报告
        """
        analysis_parts = [
            "=== 多批次风险分析综合报告 ===",
            f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "分析方法: 多批次分析",
            f"总批次数: {len(batch_results)}",
            "",
            "=== 综合风险评估 ===",
            f"风险评分: {avg_risk_score:.2f}/100",
            f"风险等级: {final_risk_level}",
            f"违约概率: {default_probability:.2%}",
            "",
            "=== 各批次风险分布 ==="
        ]

        # 添加各批次的风险概况
        for result in batch_results:
            if 'error' not in result:
                batch_num = result.get('batch_number', 'unknown')
                batch_score = result.get('batch_risk_score', 'unknown')
                batch_level = result.get('batch_risk_level', 'unknown')
                analysis_parts.append(f"批次 {batch_num}: {batch_score}分 ({batch_level})")

        analysis_parts.extend([
            "",
            "=== 关键发现汇总 ===",
        ])

        # 汇总关键发现
        for i, result in enumerate(batch_results, 1):
            if 'error' not in result and result.get('key_findings'):
                analysis_parts.append(f"批次 {i}: {result['key_findings']}")

        analysis_parts.extend([
            "",
            "=== 风险建议 ===",
        ])

        # 根据风险等级提供建议
        if final_risk_level == "高风险":
            analysis_parts.extend([
                "• 建议拒绝放贷或要求额外担保",
                "• 需要进一步核实客户信息",
                "• 建议降低授信额度"
            ])
        elif final_risk_level == "中风险":
            analysis_parts.extend([
                "• 可考虑放贷但需加强风控措施",
                "• 建议适中的授信额度",
                "• 需要定期监控还款情况"
            ])
        else:
            analysis_parts.extend([
                "• 客户风险较低，可正常放贷",
                "• 可给予较高的授信额度",
                "• 建议建立长期合作关系"
            ])

        return "\n".join(analysis_parts)
