"""
向量化服务响应模型
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
from shared.models.common import BaseResponse
from shared.models.file_info import FileStatus


class UploadResponse(BaseResponse):
    """文件上传响应"""

    file_code: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    status: Optional[FileStatus] = None


class VectorizeResponse(BaseResponse):
    """向量化响应"""

    file_code: Optional[str] = None
    status: Optional[FileStatus] = None
    document_count: Optional[int] = None
    processing_time: Optional[float] = None


class StatusResponse(BaseResponse):
    """状态查询响应"""

    file_code: Optional[str] = None
    file_name: Optional[str] = None
    status: Optional[FileStatus] = None
    document_count: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class HealthResponse(BaseModel):
    """健康检查响应"""

    status: str = "healthy"
    service: str = "vector-service"
    version: str = "1.0.0"
    timestamp: datetime = datetime.now()
    checks: Dict[str, bool] = {}

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}
