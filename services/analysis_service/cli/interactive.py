"""
交互式分析器 - 保持原有用户体验
"""

import sys
from typing import Optional
from pathlib import Path

from shared.utils.logger import get_logger
from services.analysis_service.core.risk_analyzer import RiskAnalyzer
from services.analysis_service.core.chroma_retriever import ChromaRetriever

logger = get_logger(__name__)


class InteractiveAnalyzer:
    """交互式分析器 - 兼容原有界面"""

    def __init__(self):
        """初始化交互式分析器"""
        self.risk_analyzer = RiskAnalyzer()
        self.chroma_retriever = ChromaRetriever()

        print("🚀 Deep Risk RAG 风险分析系统 (微服务版本)")
        print("=" * 50)
        print("✅ 系统初始化完成")
        print("📊 支持CSV/Excel文件风险分析")
        print("🔍 基于BGE-M3向量检索和DeepSeek分析")
        print()

    def run(self):
        """运行交互式界面"""
        while True:
            try:
                print("\n" + "=" * 50)
                print("请选择操作:")
                print("1. 查看可用文件")
                print("2. 分析指定文件")
                print("3. 查看分析历史")
                print("4. 搜索文件数据")
                print("5. 系统状态检查")
                print("0. 退出系统")
                print("=" * 50)

                choice = input("请输入选项 (0-5): ").strip()

                if choice == "0":
                    print("👋 感谢使用 Deep Risk RAG 系统！")
                    break
                elif choice == "1":
                    self._list_files()
                elif choice == "2":
                    self._analyze_file()
                elif choice == "3":
                    self._show_analysis_history()
                elif choice == "4":
                    self._search_file_data()
                elif choice == "5":
                    self._check_system_status()
                else:
                    print("❌ 无效选项，请重新选择")

            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出系统")
                break
            except Exception as e:
                logger.error(f"交互式界面错误: {e}")
                print(f"❌ 系统错误: {e}")

    def _list_files(self):
        """列出可用文件"""
        try:
            print("\n📁 正在查询可用文件...")
            file_codes = self.chroma_retriever.list_available_files()

            if not file_codes:
                print("📭 暂无可用文件")
                print("💡 提示: 请先通过向量服务上传文件")
                return

            print(f"\n📊 找到 {len(file_codes)} 个可用文件:")
            print("-" * 60)

            for i, file_code in enumerate(file_codes, 1):
                file_info = self.chroma_retriever.get_file_info(file_code)
                if file_info:
                    doc_count = file_info.get("document_count", "未知")
                    print(f"{i:2d}. {file_code} (文档数: {doc_count})")
                else:
                    print(f"{i:2d}. {file_code}")

            print("-" * 60)

        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            print(f"❌ 获取文件列表失败: {e}")

    def _analyze_file(self):
        """分析指定文件"""
        try:
            # 获取文件编码
            file_code = input("\n请输入文件编码: ").strip()
            if not file_code:
                print("❌ 文件编码不能为空")
                return

            # 检查文件是否存在
            file_info = self.chroma_retriever.get_file_info(file_code)
            if not file_info:
                print(f"❌ 文件 {file_code} 不存在")
                print("💡 提示: 使用选项1查看可用文件")
                return

            print(f"\n🔍 开始分析文件: {file_code}")
            print(f"📊 文档数量: {file_info.get('document_count', '未知')}")
            print("-" * 50)

            # 执行分析
            result = self.risk_analyzer.analyze_risk(file_code)

            # 显示结果
            self._display_analysis_result(result)

        except Exception as e:
            logger.error(f"文件分析失败: {e}")
            print(f"❌ 分析失败: {e}")

    def _display_analysis_result(self, result):
        """显示分析结果"""
        print("\n" + "=" * 60)
        print("🎯 风险分析结果")
        print("=" * 60)

        print(f"📋 分析ID: {result.analysis_id}")
        print(f"📁 文件编码: {result.file_code}")
        print(f"📊 状态: {result.status}")

        if result.default_probability is not None:
            print(f"⚠️  违约概率: {result.default_probability:.2%}")

        if result.risk_level:
            risk_emoji = {"low": "🟢", "medium": "🟡", "high": "🟠", "very_high": "🔴"}
            emoji = risk_emoji.get(result.risk_level, "❓")
            print(f"{emoji} 风险等级: {result.risk_level}")

        if result.risk_score is not None:
            print(f"📈 风险评分: {result.risk_score:.2f}")

        if result.confidence_score is not None:
            print(f"🎯 置信度: {result.confidence_score:.2%}")

        if result.processing_time is not None:
            print(f"⏱️  处理时间: {result.processing_time:.2f}秒")

        if result.key_factors:
            print(f"\n🔑 关键风险因素:")
            for i, factor in enumerate(result.key_factors, 1):
                print(f"   {i}. {factor}")

        if result.recommendations:
            print(f"\n💡 风险缓解建议:")
            for i, rec in enumerate(result.recommendations, 1):
                print(f"   {i}. {rec}")

        if result.analysis_summary:
            print(f"\n📝 分析摘要:")
            print("-" * 40)
            print(result.analysis_summary)

        print("=" * 60)

    def _show_analysis_history(self):
        """显示分析历史"""
        try:
            print("\n📚 正在查询分析历史...")

            # 简化实现：读取结果目录
            import json

            results_dir = self.risk_analyzer.results_dir

            if not results_dir.exists():
                print("📭 暂无分析历史")
                return

            results = []
            for result_file in results_dir.glob("*.json"):
                try:
                    with open(result_file, "r", encoding="utf-8") as f:
                        data = json.load(f)
                    results.append(data)
                except Exception:
                    continue

            if not results:
                print("📭 暂无分析历史")
                return

            # 按时间排序
            results.sort(key=lambda x: x.get("created_at", ""), reverse=True)

            print(f"\n📊 找到 {len(results)} 条分析记录:")
            print("-" * 80)

            for i, result in enumerate(results[:10], 1):  # 只显示最近10条
                file_code = result.get("file_code", "未知")
                status = result.get("status", "未知")
                prob = result.get("default_probability")
                created_at = (
                    result.get("created_at", "").split("T")[0]
                    if result.get("created_at")
                    else "未知"
                )

                prob_str = f"{prob:.2%}" if prob is not None else "未知"
                print(
                    f"{i:2d}. {file_code} | {status} | 违约概率: {prob_str} | {created_at}"
                )

            print("-" * 80)

        except Exception as e:
            logger.error(f"查询分析历史失败: {e}")
            print(f"❌ 查询历史失败: {e}")

    def _search_file_data(self):
        """搜索文件数据"""
        try:
            file_code = input("\n请输入文件编码: ").strip()
            if not file_code:
                print("❌ 文件编码不能为空")
                return

            query = input("请输入搜索关键词: ").strip()
            if not query:
                print("❌ 搜索关键词不能为空")
                return

            print(f"\n🔍 正在搜索文件 {file_code} 中的 '{query}'...")

            documents = self.chroma_retriever.retrieve_documents(
                file_code=file_code, query=query, top_k=5
            )

            if not documents:
                print("📭 未找到相关数据")
                return

            print(f"\n📊 找到 {len(documents)} 个相关文档:")
            print("-" * 60)

            for i, doc in enumerate(documents, 1):
                content = doc.get("content", "")
                similarity = doc.get("similarity", 0)

                # 截断显示
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"\n{i}. 相似度: {similarity:.3f}")
                print(f"   内容: {preview}")

            print("-" * 60)

        except Exception as e:
            logger.error(f"搜索文件数据失败: {e}")
            print(f"❌ 搜索失败: {e}")

    def _check_system_status(self):
        """检查系统状态"""
        try:
            print("\n🔧 正在检查系统状态...")

            checks = self.risk_analyzer.health_check()

            print("\n📊 系统组件状态:")
            print("-" * 40)

            for component, status in checks.items():
                status_emoji = "✅" if status else "❌"
                status_text = "正常" if status else "异常"
                print(f"{status_emoji} {component}: {status_text}")

            overall_status = all(checks.values())
            overall_emoji = "✅" if overall_status else "❌"
            overall_text = "健康" if overall_status else "异常"

            print("-" * 40)
            print(f"{overall_emoji} 整体状态: {overall_text}")

        except Exception as e:
            logger.error(f"系统状态检查失败: {e}")
            print(f"❌ 状态检查失败: {e}")


def main():
    """主函数"""
    try:
        analyzer = InteractiveAnalyzer()
        analyzer.run()
    except Exception as e:
        logger.error(f"交互式分析器启动失败: {e}")
        print(f"❌ 系统启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
