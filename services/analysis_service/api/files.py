"""
文件管理API路由
"""

from fastapi import APIRouter, HTTPException

from shared.utils.logger import get_logger
from services.analysis_service.models.responses import FileListResponse
from services.analysis_service.core.risk_analyzer import RiskAnalyzer

logger = get_logger(__name__)

router = APIRouter(prefix="/files", tags=["files"])

# 全局实例
risk_analyzer = RiskAnalyzer()


@router.get("/", response_model=FileListResponse)
async def list_available_files():
    """
    列出所有可用的文件

    Returns:
        文件列表
    """
    try:
        file_codes = risk_analyzer.chroma_retriever.list_available_files()

        # 获取每个文件的详细信息
        files = []
        for file_code in file_codes:
            file_info = risk_analyzer.chroma_retriever.get_file_info(file_code)
            if file_info:
                files.append(file_info)

        return FileListResponse(
            success=True,
            message=f"Found {len(files)} available files",
            files=files,
            total=len(files),
        )

    except Exception as e:
        logger.error(f"列出可用文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list files: {str(e)}")


@router.get("/{file_code}")
async def get_file_info(file_code: str):
    """
    获取文件信息

    Args:
        file_code: 文件编码

    Returns:
        文件信息
    """
    try:
        file_info = risk_analyzer.chroma_retriever.get_file_info(file_code)

        if not file_info:
            raise HTTPException(status_code=404, detail=f"File {file_code} not found")

        return {"success": True, "file_info": file_info}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get file info: {str(e)}"
        )


@router.get("/{file_code}/preview")
async def preview_file_data(file_code: str, limit: int = 3):
    """
    预览文件数据

    Args:
        file_code: 文件编码
        limit: 预览文档数量

    Returns:
        文件数据预览
    """
    try:
        # 检索少量文档用于预览
        documents = risk_analyzer.chroma_retriever.retrieve_documents(
            file_code=file_code, query="数据预览", top_k=limit
        )

        if not documents:
            raise HTTPException(
                status_code=404, detail=f"No data found for file {file_code}"
            )

        # 格式化预览数据
        preview_data = []
        for i, doc in enumerate(documents):
            preview_data.append(
                {
                    "chunk_index": i,
                    "content_preview": (
                        doc.get("content", "")[:500] + "..."
                        if len(doc.get("content", "")) > 500
                        else doc.get("content", "")
                    ),
                    "metadata": doc.get("metadata", {}),
                    "similarity": doc.get("similarity", 0),
                }
            )

        return {
            "success": True,
            "file_code": file_code,
            "preview_data": preview_data,
            "total_chunks": len(documents),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预览文件数据失败: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to preview file data: {str(e)}"
        )


@router.post("/{file_code}/search")
async def search_file_data(file_code: str, query: str, top_k: int = 5):
    """
    搜索文件数据

    Args:
        file_code: 文件编码
        query: 搜索查询
        top_k: 返回结果数量

    Returns:
        搜索结果
    """
    try:
        if not query.strip():
            raise HTTPException(status_code=400, detail="Query cannot be empty")

        documents = risk_analyzer.chroma_retriever.retrieve_documents(
            file_code=file_code, query=query, top_k=top_k
        )

        return {
            "success": True,
            "file_code": file_code,
            "query": query,
            "results": documents,
            "total_found": len(documents),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索文件数据失败: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to search file data: {str(e)}"
        )
