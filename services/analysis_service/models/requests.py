"""
分析服务请求模型
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel


class AnalysisRequest(BaseModel):
    """风险分析请求"""
    analysis_type: str = "default"
    options: Optional[Dict[str, Any]] = None
    priority: int = 1
    callback_url: Optional[str] = None
    enable_streaming: Optional[bool] = None


class BatchAnalysisRequest(BaseModel):
    """批量分析请求"""
    file_codes: List[str]
    analysis_type: str = "default"
    options: Optional[Dict[str, Any]] = None
    priority: int = 1
