"""
风控分析专用配置模块
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from enum import Enum
import logging

# 设置日志
logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险等级枚举"""

    LOW = "低风险"
    MEDIUM = "中等风险"
    HIGH = "高风险"
    CRITICAL = "极高风险"


# 删除不再需要的指标枚举，因为AI会自动分析Excel中的任意字段


# 删除不再需要的风险阈值类，因为AI会自动评估风险


class RiskAnalysisConfig:
    """风控分析配置类"""

    # 个人信贷风险评估维度权重
    RISK_WEIGHTS = {
        "收入能力": 0.30,
        "负债状况": 0.25,
        "信用历史": 0.20,
        "个人稳定性": 0.15,
        "资产状况": 0.10,
    }

    # 指令模板缓存
    _prompt_cache: Optional[str] = None
    _prompt_file_path: Optional[str] = None
    _prompt_file_mtime: Optional[float] = None  # 文件修改时间

    # 删除不再需要的风险阈值配置，因为AI会自动评估风险

    # 个人信贷风控分析prompt模板
    RISK_ANALYSIS_PROMPT = """
    # 个人信贷风控分析Prompt模板 (匿名化数据版)

**角色与目标：**
你是一位资深的信贷风控策略与数据分析专家。你的核心任务是基于一份**已匿名化处理**的个人风险数据集，进行一次独立、客观、全面的违约风险评估。产出的报告必须严格基于所提供的数据，逻辑严密，结论明确，并能直接支持匿名的信贷审批决策。
**核心约束：**
1.  **数据源唯一性：** 所有分析和结论**只能**来源于 `{personal_credit_data}`。
2.  **绝对匿名化：** 报告中**严禁**出现或推断任何可识别的个人身份信息（如姓名、身份证号、手机号、具体工作单位、详细住址等）。所有指代均使用“该申请人”或“该客户”。

**请严格按照以下结构和要求输出一份专业的个人信贷风险评估报告：**
---
### **个人信贷风险评估报告**
---

#### **1. 违约概率 (PD) 与信用评分评估**
- **核心评估结论：**
    - **预估违约概率 (Probability of Default, PD):** [给出一个具体的、有数据支撑的PD估算值或范围，例如：'3.0% - 4.5%']
    - **内部信用评分 (Credit Score):** [给出一个综合评分，例如：'650/1000']
- **评估方法与依据：**
    - 简述你是如何结合申请人数据与行业风控模型理念来得出上述PD和分数的。
    - **量化依据：** 必须引用数据中的**至少3个关键正负向指标**来佐证你的PD估算。例如：“该PD估算主要基于其‘多头借贷’维度下较高的近期查询频率（负面）、‘逾期风险’维度下存在历史M2逾期记录（负面），尽管其‘还款能力’维度下的收入负债比尚在可接受范围（中性），但整体风险偏高。”

#### **2. 综合风险等级判定**
- **综合风险等级：** **[低风险 / 中低风险 / 中等风险 / 中高风险 / 极高风险]**
- **风险等级判定依据：**
    - **概述：** 总结判定此风险等级的核心逻辑。该等级必须与第一部分的违约概率评估结果严格对应。
    - **关键权衡：** 清晰阐述是哪些**正面因素（稳定器）**和**负面因素（风险点）**相互作用，最终将风险等级确定在此水平。例如：“综合评定为‘中高风险’。主要判定依据是，虽然申请人的‘稳定性’指标（如在职时长）表现良好，但其在‘多头借贷’和‘逾期风险’这两个核心信用行为维度上展现出的高风险特征，显著超过了其稳定性带来的正面影响。”

#### **3. 关键因素分析 (按指定维度)**
*在此部分，请对以下每个维度进行深入、客观的分析，并引用具体数据指标（如存在）。*

- **身份证认证风险相关：**
    - [分析认证结果。例如：认证结果是否为“一致”，是否存在公安库无此人记录、或与申请信息不匹配等风险信号。]
- **司法风险相关：**
    - [分析司法黑名单数据。例如：是否命中“失信被执行人”、“限制高消费”名单，是否存在涉诉记录等，并说明这些记录的严重程度。]
- **还款能力：**
    - [评估收入水平与稳定性（如基于流水数据的月均值和波动性），并计算或评估其**债务收入比（DTI）**，判断其债务负担和未来履约的财务基础。]
- **交易行为：**
    - [分析与信贷相关的交易特征。例如：信用卡使用模式（如是否频繁全额还款、最低还款额使用率）、消费行为稳定性等，评估其消费习惯和财务管理能力。]
- **稳定性：**
    - [**职业稳定性：** 分析匿名化的工作信息，如在现单位类别（如国企、民企、上市公司）的工作时长。 **居住稳定性：** 分析现居住地的稳定性，如居住时长、房产所有权状况（如自有、租赁）。]
- **多头借贷：**
    - [分析多头借贷的严重程度：包括过去一段时间内（如3个月）在多少家机构申请过贷款（查询频率），当前在多少家机构有在贷余额（共债广度），评估其信贷需求的迫切性和潜在的“以贷养贷”风险。]
- **利率偏好：**
    - [分析该申请人历史或现有贷款产品的利率水平。若其信贷组合中高利率产品（如现金贷、部分消费分期）占比较高，则可能反映其融资渠道受限或风险接受度高，需作为风险点考量。]
- **反欺诈风险：**
    - [分析非身份类的欺诈信号。例如：申请设备是否存在异常（如模拟器、越狱/Root设备），申请IP是否位于高风险区域或为代理IP，申请行为（如资料填写速度）是否存在机器行为特征。]
- **逾期风险：**
    - [详细分析历史逾期记录。评估**逾期的严重性**（如M1-M2或M3+）、**逾期频率**、**最近一次逾期发生的时间**以及**累计逾期次数**。这是评估申请人信用观念和还款意愿最核心的指标。]

#### **4. 关键风险因子提炼与归因**
- **主要风险贡献因子 (负面)：** [识别并列出**Top 3** 对本次风险评估结果产生最重要负面影响的风险点，并进行归因分析。例如：1. **高频多头查询：** 反映了强烈的资金渴求，可能是现有债务链断裂的先兆。 2. **历史M3+逾期：** 表明申请人曾有严重违约行为，还款意愿和能力存在重大缺陷。]
- **主要风险缓释因子 (正面)：** [识别并列出对冲风险、支撑授信的主要正面因素。例如：1. **较低的债务收入比（DTI<30%）：** 表明其现有收入完全有能力覆盖新增债务。 2. **长期稳定的职业状态（在职>5年）：** 提供了稳定的未来还款来源预期。]

#### **5. 关键风险预警信号 (Red Flags)**
- **预警信号列表：** [清晰、简洁地列出本次评估中识别出的具体风险预警信号。若无则明确说明“未发现显著风险预警信号”。]
    - *示例1：近1个月内在超过5家非银机构有信贷查询记录。*
    - *示例2：命中司法涉诉名单，案由为“民间借贷纠纷”。*
    - *示例3：申请设备指纹关联到多个其他风险申请。*

#### **6. 授信决策与风险管理建议**
- **授信结论：** **[建议批准 / 建议附条件批准 / 建议拒绝]**
- **决策核心依据：** [用1-2句话高度概括做出此授信结论的根本原因，与前文分析保持一致。]
- **具体授信方案建议 (若批准)：**
    - **建议授信额度 (Credit Limit):** [给出一个具体金额，并说明额度厘定的逻辑，如“建议额度为人民币XX元，确保新增月供后，总DTI不超过45%”。]
    - **建议利率/费率 (Interest Rate):** [基于风险等级进行风险定价，给出具体年化利率范围，如“建议年化利率不低于X%，用于覆盖其中高风险等级对应的预期损失”。]
- **风险缓释与管理措施：**
    - **贷前措施 (若附条件批准):** [提出具体的、不依赖PII的风险控制要求。例如：“建议调低初始授信额度至XX元，设置3-6个月的观察期”、“要求提供经过脱敏处理的补充财力证明（如资产截图）”。]
    - **贷后管理关注点 (Post-Loan Monitoring):** [提出贷后需要重点监控的匿名化数据指标。例如：“持续监控该客户后续的多头借贷行为和征信查询频率”、“对其在其他金融平台的信贷行为变化保持高度警惕”。]
    """

    # 风险报告输出格式
    RISK_REPORT_FORMAT = {
        "file_code": "",
        "analysis_timestamp": "",
        "default_probability": 0.0,
        "risk_level": "",
        "risk_score": 0.0,
        "key_indicators": {},
        "risk_factors": [],
        "warning_signals": [],
        "recommendations": [],
        "analysis_summary": "",
        "data_source": "",
    }

    # 注意：不需要预设字段映射，直接将Excel内容作为知识库
    # Excel文件包含各种风控特征指标，让AI自动分析

    @classmethod
    def _get_default_prompt_file_path(cls) -> str:
        """获取默认指令文件路径"""
        # 优先级：环境变量 > 配置文件 > 默认路径
        file_path = os.getenv("RISK_ANALYSIS_PROMPT_FILE")

        if file_path is None:
            try:
                from config import settings
                file_path = settings.RISK_ANALYSIS_PROMPT_FILE
            except ImportError:
                logger.warning("无法导入配置，使用默认指令文件路径")
                file_path = "prompts/risk_analysis/default.md"

        return file_path

    @classmethod
    def load_prompt_from_file(cls, file_path: Optional[str] = None) -> Optional[str]:
        """
        从文件加载指令模板，支持缓存和文件修改时间检查

        Args:
            file_path: 指令文件路径，如果为None则使用配置中的路径

        Returns:
            指令模板内容，如果文件不存在或读取失败则返回None
        """
        if file_path is None:
            file_path = cls._get_default_prompt_file_path()

        try:
            # 获取项目根目录和文件路径
            current_dir = Path(__file__).parent.parent
            prompt_file = current_dir / file_path

            if not prompt_file.exists():
                logger.warning(f"指令文件不存在: {prompt_file}")
                return None

            # 获取文件修改时间
            file_mtime = prompt_file.stat().st_mtime

            # 缓存检查：路径相同且文件未修改
            if (cls._prompt_file_path == file_path and
                cls._prompt_cache is not None and
                cls._prompt_file_mtime == file_mtime):
                return cls._prompt_cache

            # 读取文件内容
            with open(prompt_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 更新缓存
            cls._prompt_cache = content
            cls._prompt_file_path = file_path
            cls._prompt_file_mtime = file_mtime

            logger.info(f"✅ 成功从文件加载指令模板: {prompt_file}")
            return content

        except FileNotFoundError:
            logger.warning(f"指令文件不存在: {file_path}")
            return None
        except PermissionError:
            logger.error(f"无权限读取指令文件: {file_path}")
            return None
        except UnicodeDecodeError:
            logger.error(f"指令文件编码错误，请确保使用UTF-8编码: {file_path}")
            return None
        except Exception as e:
            logger.error(f"读取指令文件失败: {e}")
            return None

    @classmethod
    def get_risk_analysis_prompt(cls, personal_credit_data: str, prompt_file: Optional[str] = None) -> str:
        """
        获取个人信贷风控分析prompt

        Args:
            personal_credit_data: 个人信贷数据
            prompt_file: 可选的指令文件路径

        Returns:
            格式化后的指令内容
        """
        # 尝试从文件加载指令
        prompt_template = cls.load_prompt_from_file(prompt_file)

        # 如果文件加载失败，使用硬编码的备用指令
        if prompt_template is None:
            logger.info("使用硬编码的备用指令模板")
            prompt_template = cls.RISK_ANALYSIS_PROMPT

        return prompt_template.format(personal_credit_data=personal_credit_data)

    @classmethod
    def clear_prompt_cache(cls) -> None:
        """清除指令模板缓存"""
        cls._prompt_cache = None
        cls._prompt_file_path = None
        cls._prompt_file_mtime = None
        logger.info("✅ 指令模板缓存已清除")

    @classmethod
    def get_default_risk_report(cls) -> Dict[str, Any]:
        """获取默认风险报告格式"""
        return cls.RISK_REPORT_FORMAT.copy()

    # 删除不再需要的风险评分计算方法，因为AI会直接给出风险等级
