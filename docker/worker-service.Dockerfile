# Worker服务Dockerfile
# 专门处理BGE-M3向量化和风险分析的Celery Worker

FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY shared/ ./shared/
COPY services/worker_service/ ./services/worker_service/
COPY src/ ./src/
COPY prompts/ ./prompts/

# 创建必要的目录
RUN mkdir -p /app/models /app/data /app/logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Worker特定的环境变量
ENV C_FORCE_ROOT=1
ENV CELERY_WORKER_CONCURRENCY=1
ENV CELERY_WORKER_PREFETCH_MULTIPLIER=1
ENV CELERY_WORKER_MAX_TASKS_PER_CHILD=10

# 启动命令 - 启动Celery Worker
CMD ["celery", "-A", "services.worker_service.worker", "worker", \
     "--loglevel=info", \
     "--concurrency=1", \
     "--prefetch-multiplier=1", \
     "--max-tasks-per-child=10", \
     "--queues=vectorization,analysis,health"]
