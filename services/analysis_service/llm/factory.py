"""
LLM工厂类 - 统一创建和管理不同的LLM提供商
"""

import logging
from typing import Dict, Type, Optional
from .base import BaseLLM, LLMConfig, LLMProvider, LLMError

logger = logging.getLogger(__name__)


class LLMFactory:
    """LLM工厂类"""
    
    _providers: Dict[LLMProvider, Type[BaseLLM]] = {}
    _instances: Dict[str, BaseLLM] = {}

    @classmethod
    def register_provider(cls, provider: LLMProvider, llm_class: Type[BaseLLM]):
        """
        注册LLM提供商
        
        Args:
            provider: 提供商类型
            llm_class: LLM实现类
        """
        cls._providers[provider] = llm_class
        logger.info(f"注册LLM提供商: {provider.value}")

    @classmethod
    def create_llm(cls, config: LLMConfig, instance_key: Optional[str] = None) -> BaseLLM:
        """
        创建LLM实例
        
        Args:
            config: LLM配置
            instance_key: 实例缓存键，如果提供则会缓存实例
            
        Returns:
            LLM实例
            
        Raises:
            LLMError: 当提供商未注册或创建失败时
        """
        # 检查是否有缓存的实例
        if instance_key and instance_key in cls._instances:
            logger.debug(f"使用缓存的LLM实例: {instance_key}")
            return cls._instances[instance_key]

        # 检查提供商是否已注册
        if config.provider not in cls._providers:
            available_providers = list(cls._providers.keys())
            raise LLMError(
                f"未注册的LLM提供商: {config.provider.value}. "
                f"可用提供商: {[p.value for p in available_providers]}"
            )

        try:
            # 创建LLM实例
            llm_class = cls._providers[config.provider]
            llm_instance = llm_class(config)
            
            # 缓存实例（如果提供了缓存键）
            if instance_key:
                cls._instances[instance_key] = llm_instance
                logger.debug(f"缓存LLM实例: {instance_key}")
            
            logger.info(f"成功创建LLM实例: {config.provider.value} - {config.model}")
            return llm_instance
            
        except Exception as e:
            logger.error(f"创建LLM实例失败: {config.provider.value} - {str(e)}")
            raise LLMError(f"创建LLM实例失败: {str(e)}") from e

    @classmethod
    def get_available_providers(cls) -> list[LLMProvider]:
        """获取可用的提供商列表"""
        return list(cls._providers.keys())

    @classmethod
    def clear_cache(cls):
        """清空实例缓存"""
        cls._instances.clear()
        logger.info("已清空LLM实例缓存")

    @classmethod
    def remove_cached_instance(cls, instance_key: str):
        """移除指定的缓存实例"""
        if instance_key in cls._instances:
            del cls._instances[instance_key]
            logger.debug(f"移除缓存实例: {instance_key}")


class ConfigBuilder:
    """配置构建器 - 减少重复代码"""

    @staticmethod
    def build_config_from_settings(settings, provider: LLMProvider) -> LLMConfig:
        """从settings构建LLMConfig"""
        # 通用配置
        common_config = {
            'provider': provider,
            'temperature': getattr(settings, 'TEMPERATURE', 0.1),
            'max_tokens': getattr(settings, 'MAX_TOKENS', 4000),
        }

        # 提供商特定配置
        if provider == LLMProvider.DEEPSEEK:
            return LLMConfig(
                api_key=getattr(settings, 'DEEPSEEK_API_KEY', None),
                base_url=getattr(settings, 'DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
                model=getattr(settings, 'DEEPSEEK_MODEL', 'deepseek-reasoner'),
                **common_config
            )
        elif provider == LLMProvider.OPENAI:
            return LLMConfig(
                api_key=getattr(settings, 'OPENAI_API_KEY', None),
                base_url=getattr(settings, 'OPENAI_BASE_URL', None),
                model=getattr(settings, 'OPENAI_MODEL', 'gpt-3.5-turbo'),
                **common_config
            )
        else:
            raise LLMError(f"暂不支持从配置创建提供商: {provider.value}")


def create_llm_from_settings(settings, instance_key: Optional[str] = None, langchain_compatible: bool = True):
    """
    从配置对象创建LLM实例的便捷函数

    Args:
        settings: 配置对象（如config.py中的settings）
        instance_key: 实例缓存键
        langchain_compatible: 是否返回LangChain兼容的适配器

    Returns:
        LLM实例或LangChain适配器
    """
    # 确定使用的提供商
    provider_name = getattr(settings, 'LLM_PROVIDER', 'deepseek').lower()

    try:
        provider = LLMProvider(provider_name)
    except ValueError:
        logger.warning(f"未知的LLM提供商: {provider_name}, 使用默认的DeepSeek")
        provider = LLMProvider.DEEPSEEK

    # 使用配置构建器创建配置
    config = ConfigBuilder.build_config_from_settings(settings, provider)
    llm = LLMFactory.create_llm(config, instance_key)

    # 如果需要LangChain兼容性，返回适配器
    if langchain_compatible:
        from .langchain_adapter import create_langchain_llm
        return create_langchain_llm(llm)

    return llm


# 自动注册提供商的装饰器
def register_llm_provider(provider: LLMProvider):
    """
    注册LLM提供商的装饰器
    
    Args:
        provider: 提供商类型
    """
    def decorator(cls: Type[BaseLLM]):
        LLMFactory.register_provider(provider, cls)
        return cls
    return decorator
