"""
LangChain适配器 - 让我们的LLM工厂与LangChain兼容
"""

import logging
from typing import Any, Dict, List, Optional
from langchain_core.language_models.llms import LLM
from langchain_core.callbacks.manager import CallbackManagerForLLMRun

from .base import BaseLLM

logger = logging.getLogger(__name__)


class LangChainLLMAdapter(LLM):
    """
    LangChain适配器，将我们的BaseLLM适配为LangChain兼容的接口
    """

    def __init__(self, llm: BaseLLM, **kwargs):
        """
        初始化适配器

        Args:
            llm: 我们的BaseLLM实例
        """
        super().__init__(**kwargs)
        self._llm = llm

    @property
    def _llm_type(self) -> str:
        """返回LLM类型"""
        return f"custom_{self._llm.get_provider().value}"

    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """
        调用LLM（LangChain LLM基类要求的方法）

        Args:
            prompt: 输入提示
            stop: 停止词列表
            run_manager: 回调管理器
            **kwargs: 额外参数

        Returns:
            响应文本
        """
        try:
            # 调用我们的LLM
            response = self._llm.invoke(prompt, **kwargs)
            return response.content

        except Exception as e:
            logger.error(f"LangChain适配器调用失败: {e}")
            raise

    @property
    def _identifying_params(self) -> Dict[str, Any]:
        """返回识别参数"""
        return {
            "provider": self._llm.get_provider().value,
            "model": self._llm.get_model(),
            "temperature": self._llm.config.temperature,
            "max_tokens": self._llm.config.max_tokens,
        }


def create_langchain_llm(llm: BaseLLM) -> LangChainLLMAdapter:
    """
    创建LangChain兼容的LLM适配器

    Args:
        llm: 我们的BaseLLM实例

    Returns:
        LangChain适配器
    """
    return LangChainLLMAdapter(llm)
