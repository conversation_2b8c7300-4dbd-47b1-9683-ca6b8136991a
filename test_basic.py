#!/usr/bin/env python3
"""
基础测试
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("开始基础测试...")

try:
    print("1. 测试导入shared模块...")
    from shared.utils.logger import get_logger
    logger = get_logger(__name__)
    logger.info("shared模块导入成功")
    print("✅ shared模块导入成功")
except Exception as e:
    print(f"❌ shared模块导入失败: {e}")
    sys.exit(1)

try:
    print("2. 测试导入vector_service配置...")
    sys.path.insert(0, str(project_root / "services" / "vector_service"))
    from services.vector_service.config import config
    logger.info(f"配置加载成功: {config.service_name}")
    print(f"✅ 配置加载成功: {config.service_name}")
except Exception as e:
    print(f"❌ 配置加载失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

try:
    print("3. 测试ChromaDB本地模式...")
    from langchain_chroma import Chroma
    print("✅ ChromaDB导入成功")
except Exception as e:
    print(f"❌ ChromaDB导入失败: {e}")
    sys.exit(1)

try:
    print("4. 测试向量管理器导入...")
    from services.vector_service.core.vector_manager import VectorManager
    print("✅ 向量管理器导入成功")
except Exception as e:
    print(f"❌ 向量管理器导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("🎉 所有基础测试通过！")
