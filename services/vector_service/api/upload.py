"""
文件上传API路由
"""

import asyncio
from typing import Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, BackgroundTasks, Request
from fastapi.responses import JSONResponse

from shared.utils.logger import get_logger
from services.vector_service.models.responses import UploadResponse
from services.vector_service.core.file_processor import FileProcessor
from services.vector_service.core.vector_manager import VectorManager
from shared.models.file_info import FileStatus

logger = get_logger(__name__)

router = APIRouter(prefix="/upload", tags=["upload"])

# 延迟初始化的实例
_file_processor = None


def get_file_processor():
    """获取FileProcessor实例（延迟初始化）"""
    global _file_processor
    if _file_processor is None:
        _file_processor = FileProcessor()
    return _file_processor


def get_vector_manager_from_app(request: Request):
    """从应用状态获取VectorManager实例"""
    return request.app.state.get_vector_manager()


def get_vector_executor_from_app(request: Request):
    """从应用状态获取向量化线程池实例"""
    return request.app.state.get_vector_executor()


def sync_vectorize_process(file_path: str, file_code: str, file_info, vector_manager_factory):
    """
    同步向量化处理函数（在独立线程中执行）
    
    Args:
        file_path: 文件路径
        file_code: 文件编码
        file_info: 文件信息
        vector_manager_factory: 向量管理器工厂函数
    """
    import time
    from datetime import datetime
    
    start_time = time.time()
    file_processor = None
    
    try:
        logger.info(f"开始向量化处理: {file_code}")
        
        # 更新状态为向量化中
        file_info.status = FileStatus.VECTORIZING
        file_info.updated_at = datetime.now()
        vector_manager = vector_manager_factory()
        vector_manager._save_file_info(file_info)
        logger.info(f"状态更新为向量化中: {file_code}")
        
        # 获取实例（线程安全）
        file_processor = get_file_processor()
        
        # 处理文件
        parse_start = time.time()
        logger.info(f"开始文件解析: {file_code}")
        documents = file_processor.process_file(file_path, file_code)
        parse_time = time.time() - parse_start
        logger.info(f"文件解析完成，生成 {len(documents)} 个文档块，耗时 {parse_time:.2f}s: {file_code}")
        
        # 存储向量
        vector_start = time.time()
        logger.info(f"开始向量化存储: {file_code}")
        success = vector_manager.store_documents(file_code, documents, file_info)
        vector_time = time.time() - vector_start
        
        total_time = time.time() - start_time
        
        if success:
            logger.info(f"向量化处理完成: {file_code}, 总耗时 {total_time:.2f}s (解析: {parse_time:.2f}s, 向量化: {vector_time:.2f}s)")
        else:
            logger.error(f"向量化处理失败: {file_code}, 总耗时 {total_time:.2f}s")
            
        return success
        
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"向量化处理异常 {file_code} (耗时 {total_time:.2f}s): {e}", exc_info=True)
        
        # 更新失败状态
        try:
            file_info.status = FileStatus.FAILED
            file_info.updated_at = datetime.now()
            vector_manager = vector_manager_factory()
            vector_manager._save_file_info(file_info)
        except Exception as save_error:
            logger.error(f"保存失败状态时出错: {save_error}")
            
        return False
        
    finally:
        # 确保临时文件总是被清理
        if file_processor:
            try:
                cleanup_success = file_processor.cleanup_file(file_path)
                if cleanup_success:
                    logger.info(f"临时文件清理完成: {file_code}")
                else:
                    logger.warning(f"临时文件清理失败，文件可能不存在: {file_code}")
            except Exception as cleanup_error:
                logger.error(f"临时文件清理异常: {cleanup_error}")


async def process_file_background_async(file_path: str, file_code: str, file_info, vector_manager_factory, vector_executor):
    """
    异步后台处理文件向量化

    Args:
        file_path: 文件路径
        file_code: 文件编码
        file_info: 文件信息
        vector_manager_factory: 向量管理器工厂函数
        vector_executor: 向量化线程池
    """
    import time
    
    async_start_time = time.time()
    
    try:
        logger.info(f"启动异步向量化任务: {file_code}")
        
        # 在独立线程中执行向量化
        loop = asyncio.get_event_loop()
        success = await loop.run_in_executor(
            vector_executor,
            sync_vectorize_process,
            file_path, file_code, file_info, vector_manager_factory
        )
        
        async_total_time = time.time() - async_start_time
        
        if success:
            logger.info(f"异步向量化任务完成: {file_code}, 异步总耗时 {async_total_time:.2f}s")
        else:
            logger.error(f"异步向量化任务失败: {file_code}, 异步总耗时 {async_total_time:.2f}s")
            
    except asyncio.CancelledError:
        logger.warning(f"异步向量化任务被取消: {file_code}")
        # 不需要重新抛出，让任务优雅结束
        
    except Exception as e:
        async_total_time = time.time() - async_start_time
        logger.error(f"异步向量化任务异常 {file_code} (异步耗时 {async_total_time:.2f}s): {e}", exc_info=True)
        
        # 尝试更新状态为失败
        try:
            from datetime import datetime
            file_info.status = FileStatus.FAILED
            file_info.updated_at = datetime.now()
            vector_manager = vector_manager_factory()
            vector_manager._save_file_info(file_info)
        except Exception as save_error:
            logger.error(f"在异步异常处理中保存失败状态时出错: {save_error}")


@router.post("/", response_model=UploadResponse)
async def upload_file(
    request: Request,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    force_reprocess: bool = False,
    chunk_size: Optional[int] = None,
    chunk_overlap: Optional[int] = None,
):
    """
    上传文件并开始向量化处理

    Args:
        request: 请求对象
        background_tasks: 后台任务
        file: 上传的文件
        force_reprocess: 是否强制重新处理
        chunk_size: 文档分块大小
        chunk_overlap: 文档分块重叠

    Returns:
        上传响应
    """
    try:
        # 检查文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")

        # 读取文件数据
        file_data = await file.read()

        if not file_data:
            raise HTTPException(status_code=400, detail="Empty file")

        logger.info(f"收到文件上传: {file.filename} ({len(file_data)} bytes)")

        # 获取实例并验证
        try:
            file_processor = get_file_processor()
            vector_manager = get_vector_manager_from_app(request)
            vector_executor = get_vector_executor_from_app(request)
        except RuntimeError as e:
            logger.error(f"服务实例未初始化: {e}")
            raise HTTPException(status_code=503, detail="Service not ready, please try again later")

        # 应用分块参数（如果提供）
        if chunk_size is not None or chunk_overlap is not None:
            logger.info(f"自定义分块参数: chunk_size={chunk_size}, chunk_overlap={chunk_overlap}")
            # 这里可以传递给 file_processor，当前版本先记录日志
            
        # 保存文件
        file_path, file_info = file_processor.save_uploaded_file(
            file_data, file.filename
        )

        # 检查是否已存在
        existing_file = vector_manager.get_file_status(file_info.file_code)
        if existing_file and not force_reprocess:
            if existing_file.status == FileStatus.COMPLETED:
                return UploadResponse(
                    success=True,
                    message="File already processed",
                    file_code=existing_file.file_code,
                    file_name=existing_file.file_name,
                    status=existing_file.status,
                )

        # 更新处理状态
        file_info.status = FileStatus.PROCESSING
        vector_manager._save_file_info(file_info)

        # 启动异步后台处理任务
        background_tasks.add_task(
            process_file_background_async,
            file_path, 
            file_info.file_code, 
            file_info,
            request.app.state.get_vector_manager,
            vector_executor
        )

        logger.info(f"文件上传成功，异步处理已启动: {file_info.file_code}")

        return UploadResponse(
            success=True,
            message="File uploaded successfully, async processing started",
            file_code=file_info.file_code,
            file_name=file_info.file_name,
            file_size=file_info.file_size,
            status=file_info.status,
        )

    except ValueError as e:
        logger.error(f"File upload validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

    except Exception as e:
        logger.error(f"File upload error: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@router.get("/list")
async def list_files(request: Request):
    """
    列出所有已上传的文件

    Args:
        request: 请求对象

    Returns:
        文件列表
    """
    try:
        vector_manager = get_vector_manager_from_app(request)
        files = vector_manager.list_files()

        return {
            "success": True,
            "files": [file.dict() for file in files],
            "total": len(files),
        }

    except Exception as e:
        logger.error(f"List files error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list files: {str(e)}")


@router.delete("/{file_code}")
async def delete_file(request: Request, file_code: str):
    """
    删除文件和相关数据

    Args:
        request: 请求对象
        file_code: 文件编码

    Returns:
        删除结果
    """
    try:
        vector_manager = get_vector_manager_from_app(request)
        success = vector_manager.delete_file(file_code)

        if success:
            return {"success": True, "message": f"File {file_code} deleted successfully"}
        else:
            raise HTTPException(
                status_code=404, detail=f"File {file_code} not found or delete failed"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete file error: {e}")
        raise HTTPException(status_code=500, detail=f"Delete failed: {str(e)}")
