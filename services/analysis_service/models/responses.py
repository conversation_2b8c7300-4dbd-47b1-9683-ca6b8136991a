"""
分析服务响应模型
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel
from datetime import datetime
from shared.models.common import BaseResponse
from shared.models.analysis_result import AnalysisResult, AnalysisStatus


class AnalysisResponse(BaseResponse):
    """分析响应"""
    analysis_id: Optional[str] = None
    file_code: Optional[str] = None
    status: Optional[AnalysisStatus] = None
    estimated_time: Optional[int] = None  # 预估完成时间(秒)


class AnalysisResultResponse(BaseResponse):
    """分析结果响应"""
    analysis_result: Optional[AnalysisResult] = None


class BatchAnalysisResponse(BaseResponse):
    """批量分析响应"""
    batch_id: Optional[str] = None
    analysis_ids: Optional[List[str]] = None
    total_count: Optional[int] = None
    status: str = "submitted"


class FileListResponse(BaseResponse):
    """文件列表响应"""
    files: Optional[List[Dict[str, Any]]] = None
    total: Optional[int] = None


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str = "healthy"
    service: str = "analysis-service"
    version: str = "1.0.0"
    timestamp: datetime = datetime.now()
    checks: Dict[str, bool] = {}
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
