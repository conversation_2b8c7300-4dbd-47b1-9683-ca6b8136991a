"""
健康检查API路由
"""

from fastapi import APIRouter, Request
from services.vector_service.models.responses import HealthResponse
from services.vector_service.core.vector_manager import VectorManager
from shared.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(tags=["health"])


def get_vector_manager_from_app(request: Request):
    """从应用状态获取VectorManager实例"""
    return request.app.state.get_vector_manager()


@router.get("/health", response_model=HealthResponse)
async def health_check(request: Request):
    """
    健康检查接口

    Args:
        request: 请求对象

    Returns:
        健康状态
    """
    try:
        # 执行各组件健康检查
        vector_manager = get_vector_manager_from_app(request)
        checks = vector_manager.health_check()

        # 判断整体健康状态
        all_healthy = all(checks.values())
        status = "healthy" if all_healthy else "unhealthy"

        return HealthResponse(
            status=status, service="vector-service", version="1.0.0", checks=checks
        )

    except Exception as e:
        logger.error(f"Health check error: {e}")
        return HealthResponse(
            status="unhealthy",
            service="vector-service",
            version="1.0.0",
            checks={"error": False},
        )


@router.get("/")
async def root():
    """
    根路径

    Returns:
        服务信息
    """
    return {
        "service": "vector-service",
        "version": "1.0.0",
        "description": "Deep Risk RAG Vector Service",
        "endpoints": {
            "upload": "/upload/",
            "status": "/status/{file_code}",
            "health": "/health",
            "docs": "/docs",
        },
    }
