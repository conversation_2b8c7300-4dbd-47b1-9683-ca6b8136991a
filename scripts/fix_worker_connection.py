#!/usr/bin/env python3
"""
修复 Worker 连接问题的脚本
解决Celery Worker进程存在但无法通信的问题
"""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def print_header():
    """打印头部信息"""
    print("=" * 60)
    print("🔧 Deep Risk RAG Worker 连接修复工具")
    print("=" * 60)
    print("专门解决Worker进程存在但无法通信的问题")
    print()

def find_celery_processes():
    """查找Celery进程"""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        celery_processes = []
        for line in lines:
            if 'celery' in line and 'worker' in line and 'services.worker_service' in line:
                parts = line.split()
                if len(parts) > 1:
                    pid = parts[1]
                    celery_processes.append({
                        'pid': pid,
                        'cmd': line
                    })
        
        return celery_processes
    except Exception as e:
        print(f"❌ 查找进程失败: {e}")
        return []

def kill_celery_processes():
    """强制结束所有Celery进程"""
    processes = find_celery_processes()
    
    if not processes:
        print("✅ 没有找到Celery进程")
        return True
    
    print(f"🔍 找到 {len(processes)} 个Celery进程:")
    for proc in processes:
        print(f"  PID {proc['pid']}: {proc['cmd'][:80]}...")
    
    print("\n⚠️  正在强制结束所有Celery进程...")
    
    for proc in processes:
        try:
            pid = int(proc['pid'])
            os.kill(pid, signal.SIGTERM)
            print(f"  ✅ 结束进程 PID {pid}")
        except Exception as e:
            print(f"  ❌ 结束进程 PID {proc['pid']} 失败: {e}")
    
    # 等待进程结束
    time.sleep(3)
    
    # 检查是否还有残余
    remaining = find_celery_processes()
    if remaining:
        print(f"\n🔄 强制杀死 {len(remaining)} 个残余进程...")
        for proc in remaining:
            try:
                pid = int(proc['pid'])
                os.kill(pid, signal.SIGKILL)
                print(f"  💀 强制杀死进程 PID {pid}")
            except Exception as e:
                print(f"  ❌ 强制杀死进程 PID {proc['pid']} 失败: {e}")
    
    time.sleep(1)
    final_check = find_celery_processes()
    
    if final_check:
        print(f"❌ 仍有 {len(final_check)} 个进程无法结束")
        return False
    else:
        print("✅ 所有Celery进程已清理完毕")
        return True

def start_fresh_worker():
    """启动全新的Worker"""
    print("\n🚀 启动全新的Worker...")
    
    # 构建启动命令
    cmd = [
        "celery", "-A", "services.worker_service.worker", "worker",
        "--pool", "processes",
        "--concurrency", "1",
        "--max-tasks-per-child", "10",  
        "--loglevel", "info",
        "--prefetch-multiplier", "1"
    ]
    
    print(f"📋 启动命令: {' '.join(cmd)}")
    
    try:
        # 后台启动
        process = subprocess.Popen(
            cmd,
            cwd=str(project_root),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid  # 创建新的进程组
        )
        
        print(f"🎯 Worker已启动 (PID: {process.pid})")
        
        # 等待一下确保启动成功
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ Worker启动成功，进程运行正常")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Worker启动失败")
            print(f"stdout: {stdout.decode()}")
            print(f"stderr: {stderr.decode()}")
            return False
            
    except FileNotFoundError:
        print("❌ celery命令未找到，请确保已安装celery")
        return False
    except Exception as e:
        print(f"❌ 启动Worker失败: {e}")
        return False

def test_worker_connection():
    """测试Worker连接"""
    print("\n🔍 测试Worker连接...")
    
    try:
        # 导入项目模块测试连接
        from shared.celery_config import celery_app
        
        print(f"📋 Celery配置:")
        print(f"  Broker: {celery_app.conf.broker_url}")
        print(f"  Backend: {celery_app.conf.result_backend}")
        
        # 检查已注册的任务
        registered_tasks = [name for name in celery_app.tasks.keys() if not name.startswith('celery.')]
        print(f"  已注册任务: {registered_tasks}")
        
        # 测试inspect
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print(f"✅ Worker连接成功! 找到 {len(stats)} 个Worker:")
            for worker_name, worker_stats in stats.items():
                print(f"  - {worker_name}")
            return True
        else:
            print("❌ Worker连接失败: 没有找到活跃的Worker")
            return False
            
    except Exception as e:
        print(f"❌ 测试连接失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_task():
    """测试简单任务"""
    print("\n🎯 测试简单任务...")
    
    try:
        from shared.celery_config import celery_app
        
        # 发送健康检查任务
        task_result = celery_app.send_task('worker.tasks.health_check')
        print(f"📤 任务已发送 (ID: {task_result.id})")
        
        # 等待结果
        print("⏳ 等待任务完成...")
        result = task_result.get(timeout=15)
        
        print(f"✅ 任务执行成功! 结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 任务执行失败: {e}")
        return False

def main():
    """主函数"""
    print_header()
    
    try:
        # 步骤1: 清理现有Worker进程
        print("📋 步骤1: 清理现有Worker进程")
        if not kill_celery_processes():
            print("❌ 清理进程失败，请手动结束Worker进程")
            return False
        
        # 步骤2: 启动全新Worker
        print("\n📋 步骤2: 启动全新Worker")
        if not start_fresh_worker():
            print("❌ 启动Worker失败")
            return False
        
        # 步骤3: 测试连接
        print("\n📋 步骤3: 测试Worker连接")
        if not test_worker_connection():
            print("❌ Worker连接测试失败")
            return False
        
        # 步骤4: 测试任务执行
        print("\n📋 步骤4: 测试任务执行")
        if not test_simple_task():
            print("❌ 任务执行测试失败")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 Worker连接修复完成!")
        print("✅ Worker进程已启动并正常工作")
        print("✅ 可以正常提交和执行任务")
        print("=" * 60)
        print("\n💡 现在可以运行验证脚本测试文件向量化功能:")
        print("   python scripts/worker_step_validator.py")
        
        return True
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出修复工具")
        return False
    except Exception as e:
        print(f"\n❌ 修复过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 