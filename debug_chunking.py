#!/usr/bin/env python3
"""
调试CSV分割行为的脚本
"""

import pandas as pd
from pathlib import Path
from src.csv_risk_processor import CSVRiskProcessor


def analyze_chunking_behavior():
    """分析分割行为"""

    # 创建一些测试数据来模拟不同的情况

    # 情况1：模拟31KB文件 - 每行数据较长
    print("=== 模拟31KB文件情况（每行较长）===")
    long_row_data = {
        "user_id": [f"user_{i:06d}" for i in range(50)],
        "feature_1": [
            f"very_long_feature_value_that_makes_row_longer_{i:010d}" for i in range(50)
        ],
        "feature_2": [
            f"another_long_feature_with_descriptive_text_content_{i:010d}"
            for i in range(50)
        ],
        "feature_3": [
            f"third_feature_with_even_more_text_to_increase_length_{i:010d}"
            for i in range(50)
        ],
        "feature_4": [
            f"fourth_very_descriptive_feature_name_with_long_content_{i:010d}"
            for i in range(50)
        ],
        "risk_score": [0.1 + i * 0.01 for i in range(50)],
    }
    long_df = pd.DataFrame(long_row_data)

    # 情况2：模拟118KB文件 - 每行数据较短
    print("\n=== 模拟118KB文件情况（每行较短）===")
    short_row_data = {
        "id": list(range(200)),
        "score": [i * 0.1 for i in range(200)],
        "flag": ["A" if i % 2 == 0 else "B" for i in range(200)],
    }
    short_df = pd.DataFrame(short_row_data)

    # 创建处理器
    processor = CSVRiskProcessor()

    # 分析长行数据
    print(f"长行数据: {len(long_df)} 行, {len(long_df.columns)} 列")
    markdown_long = processor._convert_df_to_markdown(long_df, "test_long")
    print(f"转换为Markdown后的总字符数: {len(markdown_long)}")
    print(f"平均每行字符数: {len(markdown_long) / len(long_df):.1f}")

    # 显示前几行的markdown内容
    lines = markdown_long.split("\n")
    print("前10行Markdown内容:")
    for i, line in enumerate(lines[:10]):
        print(f"  {i+1}: {line[:100]}{'...' if len(line) > 100 else ''}")

    # 分析短行数据
    print(f"\n短行数据: {len(short_df)} 行, {len(short_df.columns)} 列")
    markdown_short = processor._convert_df_to_markdown(short_df, "test_short")
    print(f"转换为Markdown后的总字符数: {len(markdown_short)}")
    print(f"平均每行字符数: {len(markdown_short) / len(short_df):.1f}")

    # 测试实际分割效果
    from langchain_text_splitters import RecursiveCharacterTextSplitter

    text_splitter = RecursiveCharacterTextSplitter.from_language(
        language="markdown",
        chunk_size=1024,
        chunk_overlap=100,
    )

    long_chunks = text_splitter.split_text(markdown_long)
    short_chunks = text_splitter.split_text(markdown_short)

    print(f"\n长行数据分割结果: {len(long_chunks)} 个块")
    print(f"短行数据分割结果: {len(short_chunks)} 个块")

    # 分析每个块的内容
    print(f"\n长行数据块大小分析:")
    for i, chunk in enumerate(long_chunks[:5]):  # 只看前5个
        print(f"  块{i+1}: {len(chunk)} 字符, 包含 {chunk.count('|')} 个表格分隔符")

    print(f"\n短行数据块大小分析:")
    for i, chunk in enumerate(short_chunks[:5]):  # 只看前5个
        print(f"  块{i+1}: {len(chunk)} 字符, 包含 {chunk.count('|')} 个表格分隔符")


if __name__ == "__main__":
    analyze_chunking_behavior()
