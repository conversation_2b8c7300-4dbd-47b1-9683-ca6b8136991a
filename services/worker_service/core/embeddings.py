"""
Worker专用BGE嵌入服务
基于原向量化服务的BGE嵌入实现，针对Worker环境优化
支持系统兼容性自动检测
"""

import gc
import torch
import numpy as np
from typing import List, Optional
from pathlib import Path
import platform

from FlagEmbedding import BGEM3FlagModel
from sklearn.preprocessing import normalize

from shared.utils.logger import get_logger
from shared.system_compatibility import get_system_config
from services.worker_service.config import config

logger = get_logger(__name__)


class WorkerBGEEmbeddingService:
    """Worker专用BGE-M3嵌入服务"""
    
    def __init__(
        self,
        model_name: str = None,
        device: str = "auto",
        batch_size: int = 32,
        max_length: int = 8192,
        normalize_embeddings: bool = True,
        enable_cache: bool = False,  # Worker中通常不需要缓存
        auto_cleanup: bool = True,
        memory_monitor: bool = True,
        **kwargs,
    ):
        """
        初始化Worker专用BGE-M3嵌入服务
        """
        self.model_name = model_name or config.model_name
        self.device = self._determine_device(device)
        self.batch_size = batch_size
        self.max_length = max_length
        self.normalize_embeddings = normalize_embeddings
        self.enable_cache = enable_cache
        self.auto_cleanup = auto_cleanup
        self.memory_monitor = memory_monitor
        
        # 模型相关
        self.model = None
        self.use_fp16 = self.device == "cuda"
        
        # 内存管理
        self.cleanup_threshold = config.memory_cleanup_threshold
        self.batch_cleanup_interval = 5  # 每5个批次清理一次
        
        logger.info(f"Worker BGE嵌入服务初始化: 设备={self.device}, 批次大小={self.batch_size}")
        
        # 立即加载模型
        self._load_model()
    
    def _determine_device(self, device: str) -> str:
        """确定使用的设备 - 使用系统兼容性检测"""
        system_config = get_system_config()

        # 如果系统要求强制使用CPU
        if system_config.force_cpu:
            logger.info(f"系统兼容性检测: {system_config.system_name} 强制使用CPU模式")
            return "cpu"

        # 使用系统推荐的设备
        if device == "auto":
            recommended_device = system_config.device
            logger.info(f"系统兼容性检测: 推荐设备 {recommended_device}")
            return recommended_device

        return device
    
    def _load_model(self):
        """加载BGE-M3嵌入模型 - 仅从本地路径加载"""
        try:
            logger.info(f"正在加载 BGE-M3 嵌入模型")
            logger.info(f"本地模型路径: {self.model_name}")
            logger.info(f"设备: {self.device}")
            
            # 验证本地模型路径存在
            model_path = Path(self.model_name)
            if not model_path.exists():
                raise FileNotFoundError(f"本地模型路径不存在: {model_path}")
            
            # 检查必要的模型文件
            required_files = ["config.json", "model.safetensors", "tokenizer.json"]
            missing_files = []
            for file_name in required_files:
                if not (model_path / file_name).exists():
                    missing_files.append(file_name)
            
            if missing_files:
                raise FileNotFoundError(f"缺少必要的模型文件: {missing_files}")
            
            logger.info(f"✅ 本地模型文件验证通过")
            
            # 加载BGE-M3模型
            self.model = BGEM3FlagModel(
                str(model_path), use_fp16=self.use_fp16, device=self.device
            )
            
            logger.info("✅ BGE-M3 模型加载成功")
            logger.info("模型维度: 1024")  # BGE-M3固定维度
            logger.info(f"最大序列长度: {self.max_length}")
            logger.info(f"使用半精度: {self.use_fp16}")
            logger.info(f"设备: {self.device}")
            
        except Exception as e:
            logger.error(f"❌ BGE-M3 模型加载失败: {e}")
            
            # 模型加载失败时清理可能的部分加载状态
            if hasattr(self, 'model'):
                self.model = None
            
            # 清理可能占用的显存
            gc.collect()
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.empty_cache()
            
            # 提供详细的错误信息
            error_msg = f"无法从本地路径加载 BGE-M3 嵌入模型: {e}\n\n"
            error_msg += "故障排除建议:\n"
            error_msg += "1. 确保已安装 FlagEmbedding 库: pip install FlagEmbedding\n"
            error_msg += "2. 检查本地模型路径是否正确\n"
            error_msg += "3. 验证模型文件完整性（config.json, model.safetensors, tokenizer.json）\n"
            error_msg += "4. 检查磁盘空间，BGE-M3 需要约 2.3GB 空间\n"
            error_msg += f"5. 当前模型路径: {self.model_name}\n"
            
            if "CUDA" in str(e) or "cuda" in str(e):
                error_msg += "6. CUDA 相关错误：尝试设置 device='cpu' 或检查 CUDA 安装\n"
            
            if "memory" in str(e).lower():
                error_msg += "6. 内存不足：尝试设置 use_fp16=True 或使用更小的 batch_size\n"
            
            raise RuntimeError(error_msg)
    
    def get_memory_info(self) -> dict:
        """获取内存使用信息"""
        memory_info = {
            "cuda_available": torch.cuda.is_available(),
            "device": self.device,
        }
        
        if torch.cuda.is_available() and self.device == "cuda":
            try:
                allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
                memory_info.update({
                    "allocated": allocated,
                    "total_memory": total_memory,
                    "memory_usage_percent": (allocated / total_memory) * 100,
                })
            except Exception as e:
                logger.debug(f"获取CUDA内存信息失败: {e}")
        
        return memory_info
    
    def log_memory_usage(self, context: str = ""):
        """记录显存使用情况"""
        if not self.memory_monitor:
            return
        
        try:
            memory_info = self.get_memory_info()
            if memory_info["cuda_available"] and self.device == "cuda":
                allocated = memory_info["allocated"]
                total = memory_info["total_memory"]
                usage_percent = memory_info["memory_usage_percent"]
                
                logger.info(
                    f"🔍 GPU显存使用 [{context}]: "
                    f"{allocated:.2f}GB/{total:.2f}GB ({usage_percent:.1f}%)"
                )
                
                # 如果显存使用率过高，发出警告
                if usage_percent > self.cleanup_threshold:
                    logger.warning(
                        f"⚠️  GPU显存使用率过高: {usage_percent:.1f}% (阈值: {self.cleanup_threshold}%), "
                        f"建议进行显存清理"
                    )
            else:
                logger.debug(f"💻 CPU模式运行 [{context}]: 设备={self.device}")
        except Exception as e:
            logger.debug(f"显存监控失败: {e}")
    
    def cleanup_memory(self, force: bool = False):
        """清理内存 - 根据系统兼容性策略调整"""
        try:
            system_config = get_system_config()

            if self.memory_monitor:
                self.log_memory_usage("清理前")

            # 先同步所有GPU操作
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.synchronize()

            # 清理Python垃圾回收
            gc.collect()

            # 清理CUDA缓存
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.empty_cache()
                torch.cuda.reset_max_memory_allocated()
                torch.cuda.synchronize()
                logger.debug("✅ CUDA缓存已清理并同步")
            else:
                logger.debug("💻 CPU模式: 执行Python垃圾回收")

            # 根据内存管理策略决定是否清理模型引用
            should_cleanup_model = force

            # 线程池模式下更谨慎地清理模型（因为模型是共享的）
            if system_config.memory_strategy == "code_cleanup" and system_config.pool_type == "threads":
                # 线程池中不轻易删除模型引用，除非强制要求
                should_cleanup_model = force
                logger.debug("🧵 线程池模式: 保留共享模型引用")
            elif system_config.memory_strategy == "process_restart":
                # 进程池模式可以更激进地清理
                should_cleanup_model = force
                logger.debug("🔄 进程池模式: 依赖进程重启清理")

            # 清理模型引用
            if should_cleanup_model and self.model is not None:
                logger.info("🔄 清理模型引用...")
                del self.model
                self.model = None
                gc.collect()
                if torch.cuda.is_available() and self.device == "cuda":
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                logger.info("✅ 模型引用已清理")
            
            # 延迟确保清理生效
            import time
            time.sleep(0.1)
            
            if self.memory_monitor:
                self.log_memory_usage("清理后")
                
        except Exception as e:
            logger.warning(f"显存清理时出现警告: {e}")
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        嵌入文档列表 - Worker专用实现
        """
        if not texts:
            return []
        
        if self.model is None:
            raise RuntimeError("模型未加载")
        
        if self.memory_monitor:
            self.log_memory_usage("开始嵌入")
        
        try:
            embeddings = []
            batch_count = 0
            
            # 批处理嵌入
            for i in range(0, len(texts), self.batch_size):
                batch_texts = texts[i:i + self.batch_size]
                batch_count += 1
                
                try:
                    # 生成嵌入向量
                    batch_output = self.model.encode(
                        batch_texts,
                        batch_size=len(batch_texts),
                        max_length=self.max_length,
                        return_dense=True,
                        return_sparse=False,
                        return_colbert_vecs=False,
                    )
                    
                    # 提取密集嵌入向量
                    batch_embeddings = batch_output["dense_vecs"]
                    
                    # 立即转换为numpy数组并释放GPU张量
                    if hasattr(batch_embeddings, 'cpu'):
                        batch_embeddings = batch_embeddings.cpu().numpy()
                    elif hasattr(batch_embeddings, 'detach'):
                        batch_embeddings = batch_embeddings.detach().cpu().numpy()
                    
                    # 确保是numpy数组格式
                    batch_embeddings = np.array(batch_embeddings, dtype=np.float32)
                    
                    # 标准化（如果启用）
                    if self.normalize_embeddings:
                        batch_embeddings = normalize(batch_embeddings, norm='l2', axis=1)
                    
                    # 转换为列表格式
                    embeddings.extend(batch_embeddings.tolist())
                    
                    logger.debug(f"批次 {batch_count} 处理完成: {len(batch_texts)} 个文本")
                    
                except Exception as e:
                    logger.error(f"批次 {batch_count} 处理失败: {e}")
                    raise
                
                # 智能清理策略
                if self.auto_cleanup and batch_count % self.batch_cleanup_interval == 0:
                    gc.collect()
                    if torch.cuda.is_available() and self.device == "cuda":
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()
                    logger.debug(f"批处理清理 - 批次 {batch_count}")
            
            logger.info(f"嵌入生成完成: {len(texts)} 个文本, {batch_count} 个批次")
            return embeddings
            
        except Exception as e:
            logger.error(f"嵌入生成失败: {e}")
            # 异常情况下也要清理显存
            if self.auto_cleanup:
                self.cleanup_memory()
            raise
        finally:
            # 任务完成后自动清理
            if self.auto_cleanup:
                self.cleanup_memory()
                logger.debug("任务完成后自动清理执行")
            
            if self.memory_monitor:
                # 延迟监控确保清理生效
                if torch.cuda.is_available() and self.device == "cuda":
                    torch.cuda.synchronize()
                self.log_memory_usage("嵌入完成")
    
    def embed_query(self, text: str) -> List[float]:
        """嵌入单个查询文本"""
        return self.embed_documents([text])[0]
