"""
风险预测分析器
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
import json

from langchain_core.documents import Document

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from config import settings
    from src.file_coded_vector_store import FileCodedVectorStore
    from src.risk_config import RiskAnalysisConfig, RiskLevel
    from src.llm import create_llm_from_settings, LLMError
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.error(f"导入配置失败: {e}")

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RiskAnalyzer:
    """风险预测分析器"""

    def __init__(
        self,
        file_coded_vector_store: Optional[FileCodedVectorStore] = None,
        api_key: Optional[str] = None,
        model_name: str = "deepseek-reasoner",
        temperature: float = 0.1,
        max_tokens: int = None,
    ):
        """
        初始化风险分析器

        Args:
            file_coded_vector_store: 文件编码向量存储管理器
            api_key: DeepSeek API 密钥
            model_name: 模型名称
            temperature: 生成温度
            max_tokens: 最大生成长度
        """
        # 初始化向量存储管理器
        if file_coded_vector_store is None:
            self.vector_store_manager = FileCodedVectorStore()
        else:
            self.vector_store_manager = file_coded_vector_store

        # 配置 LLM（保持向后兼容）
        self.api_key = (
            api_key or settings.DEEPSEEK_API_KEY or os.getenv("DEEPSEEK_API_KEY")
        )
        self.model_name = model_name or settings.DEEPSEEK_MODEL
        self.temperature = temperature
        self.max_tokens = max_tokens or settings.MAX_TOKENS

        # 初始化 LLM（使用新的工厂模式，不使用LangChain适配器）
        try:
            self.llm = create_llm_from_settings(settings, instance_key="risk_analyzer", langchain_compatible=False)
            logger.info(f"✅ 使用LLM提供商: {settings.LLM_PROVIDER}")
        except LLMError as e:
            logger.error(f"LLM初始化失败: {e}")
            raise ValueError(f"LLM初始化失败: {e}") from e

        # 风控配置
        self.risk_config = RiskAnalysisConfig()

        # 流式输出配置
        self.enable_streaming = getattr(settings, 'ENABLE_STREAMING_OUTPUT', False)

        logger.info("✅ 风险分析器初始化完成")
        logger.info(f"模型: {self.model_name}")
        logger.info(f"温度: {self.temperature}")
        logger.info(f"流式输出: {'启用' if self.enable_streaming else '禁用'}")

    def _stream_analysis(self, prompt: str) -> str:
        """
        执行流式分析并收集完整内容

        Args:
            prompt: 分析提示词

        Returns:
            完整的分析结果内容
        """
        try:
            logger.info("开始流式AI分析...")
            collected_content = []

            # 流式调用LLM
            for chunk in self.llm.stream_invoke(prompt):
                if chunk:
                    # 实时输出chunk（改善用户体验）
                    print(chunk, end='', flush=True)
                    collected_content.append(chunk)

            # 输出换行符结束流式输出
            print()

            # 返回完整内容
            full_content = ''.join(collected_content)
            logger.info(f"流式分析完成，总长度: {len(full_content)}")
            return full_content

        except Exception as e:
            logger.error(f"流式分析失败: {e}")
            logger.info("回退到非流式调用...")
            # 回退到非流式调用
            response = self.llm.invoke(prompt)
            return response.content

    def _non_stream_analysis(self, prompt: str) -> str:
        """
        执行非流式分析

        Args:
            prompt: 分析提示词

        Returns:
            分析结果内容
        """
        logger.info("开始非流式AI分析...")
        response = self.llm.invoke(prompt)
        logger.info(f"非流式分析完成，长度: {len(response.content)}")
        return response.content

    def extract_risk_data_from_documents(self, documents: List[Document]) -> str:
        """
        从文档中提取风控数据，使用智能选择策略最大化数据利用率

        Args:
            documents: 文档列表

        Returns:
            格式化的风控数据字符串
        """
        if not documents:
            return "未找到相关风控数据"

        # 增加总长度限制，充分利用DeepSeek的上下文能力
        max_total_length = 160000  # 约40K tokens，为prompt和response留足空间

        # 智能文档选择策略
        selected_docs = self._select_important_documents(documents, max_total_length)

        risk_data_parts = []
        current_length = 0

        for i, doc in enumerate(selected_docs, 1):
            # 获取文档元数据
            file_name = doc.metadata.get("file_name", "未知文件")
            chunk_id = doc.metadata.get("chunk_id", "")
            sheet_name = doc.metadata.get("sheet_name", doc.metadata.get("original_sheet", ""))

            content = doc.page_content.strip()

            # 格式化文档内容，包含更多元数据信息
            chunk_info = f" (块 {chunk_id})" if chunk_id != "" else ""
            sheet_info = f" - {sheet_name}" if sheet_name else ""

            data_part = f"""
                数据源 {i}{chunk_info}{sheet_info}:
                文件: {file_name}
                内容: {content}
            ---"""

            risk_data_parts.append(data_part)
            current_length += len(data_part)

        result = "\n".join(risk_data_parts)
        logger.info(f"提取风控数据完成，总长度: {len(result)} 字符，文档数: {len(risk_data_parts)}/{len(documents)}")
        return result

    def _select_important_documents(self, documents: List[Document], max_length: int) -> List[Document]:
        """
        智能选择重要文档，最大化数据利用率

        Args:
            documents: 所有文档
            max_length: 最大总长度

        Returns:
            选择的重要文档列表
        """
        # 策略1: 优先选择包含关键风控指标的文档
        risk_keywords = [
            '收入', '负债', '信用', '逾期', '还款', '征信', '资产', '流水',
            '工资', '房贷', '车贷', '信用卡', '借款', '担保', '抵押',
            '年龄', '学历', '职业', '工作', '婚姻', '户籍', '风险', '压力',
            '极高'
        ]

        # 计算每个文档的重要性得分
        doc_scores = []
        for doc in documents:
            content = doc.page_content.lower()
            score = 0

            # 基于关键词的得分
            for keyword in risk_keywords:
                score += content.count(keyword)

            # 基于文档长度的得分（适中长度更好）
            length = len(doc.page_content)
            if 1000 <= length <= 4000:  # 理想长度范围
                score += 10
            elif length > 4000:
                score += 5

            # 基于数字密度的得分（风控数据通常包含大量数字）
            import re
            numbers = re.findall(r'\d+', content)
            score += len(numbers) * 0.1

            doc_scores.append((doc, score, length))

        # 按得分排序
        doc_scores.sort(key=lambda x: x[1], reverse=True)

        # 贪心选择：在长度限制内选择尽可能多的高分文档
        selected_docs = []
        current_length = 0

        for doc, score, length in doc_scores:
            # 估算格式化后的长度（添加一些开销）
            estimated_length = length + 100  # 格式化开销

            if current_length + estimated_length <= max_length:
                selected_docs.append(doc)
                current_length += estimated_length
            else:
                # 尝试压缩内容以包含更多文档
                remaining_space = max_length - current_length
                if remaining_space > 500:  # 至少要有500字符才值得压缩
                    compressed_doc = self._compress_document(doc, remaining_space - 100)
                    if compressed_doc:
                        selected_docs.append(compressed_doc)
                        break

        logger.info(f"智能选择了 {len(selected_docs)}/{len(documents)} 个文档，预计长度: {current_length}")
        return selected_docs

    def _compress_document(self, doc: Document, max_length: int) -> Optional[Document]:
        """
        压缩文档内容，保留关键信息

        Args:
            doc: 原始文档
            max_length: 最大长度

        Returns:
            压缩后的文档或None
        """
        content = doc.page_content
        if len(content) <= max_length:
            return doc

        # 简单的压缩策略：保留前半部分和后半部分
        half_length = (max_length - 50) // 2  # 留50字符给省略号
        compressed_content = (
            content[:half_length] +
            "\n...(中间内容已省略)...\n" +
            content[-half_length:]
        )

        # 创建压缩后的文档
        compressed_doc = Document(
            page_content=compressed_content,
            metadata=doc.metadata.copy()
        )
        compressed_doc.metadata['compressed'] = True

        return compressed_doc

    def extract_default_probability_from_analysis(self, analysis_text: str) -> float:
        """
        从AI分析结果中提取违约概率

        Args:
            analysis_text: AI分析文本

        Returns:
            违约概率 (0-1之间)
        """
        # 尝试从分析文本中提取违约概率
        import re

        # 查找百分比格式的违约概率
        percentage_match = re.search(r"违约概率[：:]\s*([0-9.]+)%", analysis_text)
        if percentage_match:
            try:
                return float(percentage_match.group(1)) / 100
            except ValueError:
                pass

        # 查找小数格式的违约概率
        decimal_match = re.search(r"违约概率[：:]\s*([0-9.]+)", analysis_text)
        if decimal_match:
            try:
                prob = float(decimal_match.group(1))
                if prob <= 1.0:
                    return prob
                else:
                    return prob / 100  # 假设是百分比形式
            except ValueError:
                pass

        # 根据风险等级估算违约概率
        if "极高风险" in analysis_text:
            return 0.8
        elif "高风险" in analysis_text:
            return 0.6
        elif "中等风险" in analysis_text:
            return 0.3
        elif "低风险" in analysis_text:
            return 0.1
        else:
            return 0.5  # 默认中等风险

    def extract_risk_level_from_analysis(self, analysis_text: str) -> str:
        """
        从分析文本中提取风险等级

        Args:
            analysis_text: AI分析文本

        Returns:
            风险等级字符串
        """
        # 按优先级查找风险等级
        if "极高风险" in analysis_text:
            return "极高风险"
        elif "高风险" in analysis_text:
            return "高风险"
        elif "中等风险" in analysis_text or "中风险" in analysis_text:
            return "中等风险"
        elif "低风险" in analysis_text:
            return "低风险"
        else:
            return "中等风险"  # 默认

    def generate_risk_report(self, file_code: str) -> Dict[str, Any]:
        """
        生成风险分析报告

        Args:
            file_code: 文件编码

        Returns:
            完整的风险分析报告
        """
        try:
            logger.info(f"开始为文件编码 {file_code} 生成风险报告")

            # 获取文件对应的所有文档
            documents = self.vector_store_manager.get_all_documents_by_code(file_code)

            if not documents:
                return {
                    "success": False,
                    "error": f"未找到文件编码 {file_code} 对应的数据",
                    "file_code": file_code,
                }

            # 提取风控数据
            risk_data = self.extract_risk_data_from_documents(documents)

            # 生成风控分析prompt
            analysis_prompt = self.risk_config.get_risk_analysis_prompt(risk_data)
            # logger.info(analysis_prompt)

            # 调用AI进行分析（根据配置选择流式或非流式）
            if self.enable_streaming:
                analysis_content = self._stream_analysis(analysis_prompt)
            else:
                analysis_content = self._non_stream_analysis(analysis_prompt)

            # 从AI分析结果中提取信息
            default_probability = self.extract_default_probability_from_analysis(
                analysis_content
            )
            risk_level = self.extract_risk_level_from_analysis(analysis_content)

            # 简化的风险评分（基于违约概率）
            risk_score = default_probability

            # 获取文件信息
            file_info = self.vector_store_manager.get_file_info_by_code(file_code)

            # 构建完整报告
            risk_report = {
                "success": True,
                "file_code": file_code,
                "analysis_timestamp": datetime.now().isoformat(),
                "default_probability": round(default_probability, 4),
                "risk_level": risk_level,
                "risk_score": round(risk_score, 4),
                "analysis_summary": analysis_content,
                "document_count": len(documents),
                "data_source": file_info.get("file_info", {}) if file_info else {},
                "model_info": {
                    "model_name": self.model_name,
                    "temperature": self.temperature,
                },
            }

            logger.info(
                f"✅ 风险报告生成完成，违约概率: {default_probability:.2%}, 风险等级: {risk_level}"
            )
            return risk_report

        except Exception as e:
            logger.error(f"生成风险报告失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_code": file_code,
                "analysis_timestamp": datetime.now().isoformat(),
            }

    def get_risk_summary(self, file_code: str) -> str:
        """
        获取风险分析摘要

        Args:
            file_code: 文件编码

        Returns:
            风险分析摘要文本
        """
        report = self.generate_risk_report(file_code)

        if not report.get("success", False):
            return f"风险分析失败: {report.get('error', '未知错误')}"

        summary = f"""
=== 风险分析摘要 ===
文件编码: {report['file_code']}
分析时间: {report['analysis_timestamp']}
违约概率: {report['default_probability']:.2%}
风险等级: {report['risk_level']}
风险评分: {report['risk_score']:.2f}

AI分析结果:
{report['analysis_summary'][:500]}...
"""

        return summary

    def list_available_file_codes(self) -> List[str]:
        """获取所有可用的文件编码"""
        return self.vector_store_manager.list_file_codes()

    def get_analyzer_info(self) -> Dict[str, Any]:
        """获取分析器信息"""
        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "vector_store_stats": self.vector_store_manager.get_statistics(),
        }
