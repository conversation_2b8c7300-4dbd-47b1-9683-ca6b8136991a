#!/bin/bash

# Deep Risk RAG Redis配置更新脚本
# 用于设置项目特定的Redis命名空间隔离

set -e

echo "🔴 更新Deep Risk RAG项目的Redis配置..."

# 检查.env文件是否存在
if [ ! -f ".env" ]; then
    echo "📝 .env文件不存在，从.env.example创建..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
    else
        echo "❌ .env.example文件也不存在，无法创建配置"
        exit 1
    fi
fi

# 更新.env文件中的Redis配置
echo "📝 更新.env文件中的Redis配置..."

# 检查是否已存在Redis配置
if grep -q "REDIS_HOST" .env; then
    echo "⚠️  .env文件中已存在Redis配置，请手动检查和更新以下配置："
    echo "  REDIS_HOST=localhost"
    echo "  REDIS_PORT=6379"
    echo "  REDIS_DB=5"
    echo "  REDIS_PASSWORD="
    echo "  REDIS_KEY_PREFIX=deep-risk-rag"
else
    echo "➕ 添加Redis配置到.env文件..."
    cat >> .env << 'EOF'

# Redis配置 (项目隔离)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=5
REDIS_PASSWORD=
REDIS_KEY_PREFIX=deep-risk-rag
EOF
fi

echo "✅ Redis配置更新完成！"
echo ""
echo "📋 当前配置说明："
echo "  - 使用Redis数据库 5 进行项目隔离"
echo "  - 键前缀: deep-risk-rag"
echo "  - 端口: 6379 (默认)"
echo "  - 主机: localhost"
echo ""
echo "🔄 如需使用其他Redis服务器，请修改.env文件中的REDIS_HOST和REDIS_PORT" 