#!/usr/bin/env python3
"""
测试 macOS 的两种解决方案
1. 线程池方案（默认）
2. 环境变量方案（OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES）
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_solution(solution_name: str, env_vars: dict = None) -> bool:
    """测试指定的解决方案"""
    print(f"\n🧪 测试 {solution_name}")
    print("=" * 60)
    
    # 设置环境变量
    env = os.environ.copy()
    if env_vars:
        env.update(env_vars)
    
    # 测试脚本
    test_script = f"""
import sys
sys.path.insert(0, '.')
from shared.system_compatibility import get_system_config
from services.worker_service.tasks.health import health_check

print('📋 系统配置:')
config = get_system_config()
print(f'  系统: {{config.system_name}}')
print(f'  池类型: {{config.pool_type}}')
print(f'  设备: {{config.device}}')
print(f'  强制CPU: {{config.force_cpu}}')
print(f'  内存策略: {{config.memory_strategy}}')
print(f'  环境变量: {{config.env_vars}}')
print()

print('🏥 执行健康检查任务...')
try:
    result = health_check.apply()
    task_result = result.get(timeout=30)
    print('✅ 健康检查任务执行成功!')
    print(f'  整体健康状态: {{task_result.get("overall_healthy", False)}}')
    print(f'  检查耗时: {{task_result.get("check_time", 0):.2f}}秒')
    
    # 显示各组件状态
    health_status = task_result.get('health_status', {{}})
    for component, status in health_status.items():
        healthy = status.get('healthy', False)
        status_icon = "✅" if healthy else "❌"
        print(f'  {{status_icon}} {{component}}: {{"健康" if healthy else "异常"}}')
        
except Exception as e:
    print(f'❌ 健康检查任务执行失败: {{e}}')
    sys.exit(1)
"""
    
    try:
        # 运行测试
        result = subprocess.run(
            [sys.executable, '-c', test_script],
            env=env,
            capture_output=True,
            text=True,
            timeout=60,
            cwd=str(project_root)
        )
        
        print("📤 输出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {solution_name} 测试成功!")
            return True
        else:
            print(f"❌ {solution_name} 测试失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {solution_name} 测试超时")
        return False
    except Exception as e:
        print(f"❌ {solution_name} 测试异常: {e}")
        return False


def test_celery_worker(solution_name: str, env_vars: dict = None) -> bool:
    """测试 Celery Worker 启动"""
    print(f"\n🚀 测试 Celery Worker - {solution_name}")
    print("=" * 60)
    
    # 设置环境变量
    env = os.environ.copy()
    if env_vars:
        env.update(env_vars)
    
    try:
        # 获取启动参数
        get_args_script = """
import sys
sys.path.insert(0, '.')
from shared.celery_config import get_worker_startup_args
args = get_worker_startup_args()
print(' '.join([f'--{k} {v}' for k, v in args.items() if v is not None]))
"""
        
        result = subprocess.run(
            [sys.executable, '-c', get_args_script],
            env=env,
            capture_output=True,
            text=True,
            timeout=10,
            cwd=str(project_root)
        )
        
        if result.returncode != 0:
            print(f"❌ 获取启动参数失败: {result.stderr}")
            return False
        
        worker_args = result.stdout.strip()
        print(f"📋 Worker 参数: {worker_args}")
        
        # 构建启动命令
        cmd = [
            'celery', '-A', 'services.worker_service.worker', 'worker',
            '--loglevel', 'info',
            '--prefetch-multiplier', '1'
        ] + worker_args.split()
        
        print(f"🚀 启动命令: {' '.join(cmd)}")
        print("⏳ 启动 Worker（5秒后自动停止）...")
        
        # 启动 Worker
        process = subprocess.Popen(
            cmd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=str(project_root)
        )
        
        # 等待一段时间
        import time
        time.sleep(5)
        
        # 终止进程
        process.terminate()
        stdout, stderr = process.communicate(timeout=5)
        
        print("📤 Worker 输出:")
        print(stdout[-500:] if len(stdout) > 500 else stdout)
        
        if stderr:
            print("⚠️  Worker 错误输出:")
            print(stderr[-500:] if len(stderr) > 500 else stderr)
        
        # 检查是否有崩溃信息
        if 'SIGABRT' in stderr or 'MPSGraphObject' in stderr:
            print("❌ 检测到 MPS+fork 冲突")
            return False
        else:
            print("✅ 未检测到 MPS+fork 冲突")
            return True
            
    except subprocess.TimeoutExpired:
        print("⏰ Worker 测试超时")
        if 'process' in locals():
            process.kill()
        return False
    except Exception as e:
        print(f"❌ Worker 测试异常: {e}")
        return False


def main():
    """主函数"""
    print("🔬 macOS 解决方案对比测试")
    print("测试两种解决 MPS+fork 冲突的方案")
    print("=" * 80)
    
    results = []
    
    # 方案 1: 线程池方案（默认）
    print("\n🧵 方案 1: 线程池方案")
    print("优点: 彻底避免 fork 冲突，架构清晰，安全可靠")
    print("缺点: 改变了 Worker 池类型，共享内存")
    
    threads_env = {"MACOS_FORK_SOLUTION": "threads"}
    threads_health = test_solution("线程池方案 - 健康检查", threads_env)
    threads_worker = test_celery_worker("线程池方案", threads_env)
    threads_success = threads_health and threads_worker
    results.append(("线程池方案", threads_success))
    
    # 方案 2: 环境变量方案
    print("\n🌍 方案 2: 环境变量方案")
    print("优点: 保持进程池架构，配置简单")
    print("缺点: 绕过系统安全检查，可能存在潜在风险")
    
    env_var_env = {"MACOS_FORK_SOLUTION": "env_var"}
    env_var_health = test_solution("环境变量方案 - 健康检查", env_var_env)
    env_var_worker = test_celery_worker("环境变量方案", env_var_env)
    env_var_success = env_var_health and env_var_worker
    results.append(("环境变量方案", env_var_success))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{name}: {status}")
    
    print("\n💡 使用建议:")
    
    if threads_success and env_var_success:
        print("🎯 两种方案都可行！可以根据需求选择：")
        print("   - 如果追求稳定性和安全性，推荐使用线程池方案")
        print("   - 如果希望保持现有架构，可以使用环境变量方案")
    elif threads_success:
        print("🛡️  推荐使用线程池方案，更安全可靠")
    elif env_var_success:
        print("🌍 推荐使用环境变量方案，配置简单")
    else:
        print("⚠️  两种方案都有问题，需要进一步调试")
    
    print("\n🔧 配置方法:")
    print("# 使用线程池方案（默认）")
    print("export MACOS_FORK_SOLUTION=threads")
    print()
    print("# 使用环境变量方案")
    print("export MACOS_FORK_SOLUTION=env_var")
    print()
    print("# 然后启动 Worker")
    print("python services/worker_service/worker.py")


if __name__ == "__main__":
    main()
