"""
文件编码管理接口
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
import logging
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.file_coded_vector_store import FileCodedVectorStore
    from src.excel_risk_processor import ExcelRiskProcessor
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.error(f"导入配置失败: {e}")

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FileCodeManager:
    """文件编码管理器"""

    def __init__(self, base_persist_dir: str = "./cache/file_coded_chroma"):
        """
        初始化文件编码管理器

        Args:
            base_persist_dir: 向量存储基础目录
        """
        self.vector_store_manager = FileCodedVectorStore(
            base_persist_dir=base_persist_dir
        )
        self.excel_processor = ExcelRiskProcessor()

        logger.info("文件编码管理器初始化完成")

    def upload_excel_and_get_code(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        上传Excel文件并获取文件编码

        Args:
            file_path: Excel文件路径

        Returns:
            包含文件编码和处理结果的字典
        """
        try:
            file_path = Path(file_path)
            logger.info(f"开始上传Excel文件: {file_path}")

            # 验证文件
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}",
                    "file_path": str(file_path),
                }

            if file_path.suffix.lower() not in [".xlsx", ".xls"]:
                return {
                    "success": False,
                    "error": f"不支持的文件格式: {file_path.suffix}",
                    "file_path": str(file_path),
                }

            # 处理Excel文件
            file_code, documents, file_info = self.excel_processor.process_excel_file(
                file_path
            )

            if not file_code or not documents:
                return {
                    "success": False,
                    "error": "Excel文件处理失败，可能是文件格式错误或内容为空",
                    "file_path": str(file_path),
                }

            # 检查文件编码是否已存在
            existing_codes = self.vector_store_manager.list_file_codes()
            if file_code in existing_codes:
                logger.warning(f"文件编码 {file_code} 已存在，将覆盖原有数据")
                self.vector_store_manager.remove_file_code(file_code)

            # 创建向量存储
            vector_store = self.vector_store_manager.create_vector_store_for_file(
                file_code=file_code, documents=documents, file_info=file_info
            )

            result = {
                "success": True,
                "file_code": file_code,
                "file_path": str(file_path),
                "file_name": file_path.name,
                "document_count": len(documents),
                "file_size": file_path.stat().st_size,
                "upload_timestamp": datetime.now().isoformat(),
                "message": f"文件上传成功，生成编码: {file_code}",
            }

            logger.info(f"✅ Excel文件上传完成，文件编码: {file_code}")
            return result

        except Exception as e:
            logger.error(f"上传Excel文件失败: {e}")
            return {
                "success": False,
                "error": f"上传失败: {str(e)}",
                "file_path": str(file_path) if "file_path" in locals() else "unknown",
            }

    def list_available_codes(self) -> List[Dict[str, Any]]:
        """
        列出所有可用的文件编码

        Returns:
            文件编码信息列表
        """
        try:
            file_codes = self.vector_store_manager.list_file_codes()
            code_list = []

            for file_code in file_codes:
                file_info = self.vector_store_manager.get_file_info_by_code(file_code)

                code_info = {
                    "file_code": file_code,
                    "created_at": (
                        file_info.get("created_at", "未知") if file_info else "未知"
                    ),
                    "document_count": (
                        file_info.get("document_count", 0) if file_info else 0
                    ),
                    "file_info": file_info.get("file_info", {}) if file_info else {},
                }

                # 添加文件基本信息
                if file_info and "file_info" in file_info:
                    file_details = file_info["file_info"]
                    code_info.update(
                        {
                            "file_name": file_details.get("file_name", "未知"),
                            "file_size": file_details.get("file_size", 0),
                            "sheet_count": file_details.get("sheet_count", 0),
                        }
                    )

                code_list.append(code_info)

            # 按创建时间排序
            code_list.sort(key=lambda x: x.get("created_at", ""), reverse=True)

            logger.info(f"找到 {len(code_list)} 个文件编码")
            return code_list

        except Exception as e:
            logger.error(f"获取文件编码列表失败: {e}")
            return []

    def get_file_info_by_code(self, file_code: str) -> Dict[str, Any]:
        """
        根据文件编码获取文件信息

        Args:
            file_code: 文件编码

        Returns:
            文件信息字典
        """
        try:
            file_info = self.vector_store_manager.get_file_info_by_code(file_code)

            if file_info:
                return {"success": True, "file_code": file_code, "file_info": file_info}
            else:
                return {
                    "success": False,
                    "file_code": file_code,
                    "error": "文件编码不存在",
                }

        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return {"success": False, "file_code": file_code, "error": str(e)}

    def remove_file_code(self, file_code: str) -> Dict[str, Any]:
        """
        删除文件编码及其数据

        Args:
            file_code: 文件编码

        Returns:
            删除结果
        """
        try:
            # 检查文件编码是否存在
            if file_code not in self.vector_store_manager.list_file_codes():
                return {
                    "success": False,
                    "file_code": file_code,
                    "error": "文件编码不存在",
                }

            # 删除文件编码
            success = self.vector_store_manager.remove_file_code(file_code)

            if success:
                logger.info(f"✅ 成功删除文件编码: {file_code}")
                return {
                    "success": True,
                    "file_code": file_code,
                    "message": "文件编码删除成功",
                }
            else:
                return {
                    "success": False,
                    "file_code": file_code,
                    "error": "删除操作失败",
                }

        except Exception as e:
            logger.error(f"删除文件编码失败: {e}")
            return {"success": False, "file_code": file_code, "error": str(e)}

    def check_file_code_exists(self, file_code: str) -> bool:
        """
        检查文件编码是否存在

        Args:
            file_code: 文件编码

        Returns:
            是否存在
        """
        try:
            return file_code in self.vector_store_manager.list_file_codes()
        except Exception as e:
            logger.error(f"检查文件编码失败: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取文件管理统计信息

        Returns:
            统计信息字典
        """
        try:
            vector_stats = self.vector_store_manager.get_statistics()
            file_codes = self.list_available_codes()

            # 计算总文件大小
            total_size = sum(code.get("file_size", 0) for code in file_codes)

            return {
                "total_files": len(file_codes),
                "total_documents": vector_stats.get("total_documents", 0),
                "total_file_size": total_size,
                "vector_store_stats": vector_stats,
                "recent_files": file_codes[:5],  # 最近5个文件
            }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                "total_files": 0,
                "total_documents": 0,
                "total_file_size": 0,
                "error": str(e),
            }


# 便捷函数
def create_file_code_manager(
    base_persist_dir: str = "./cache/file_coded_chroma",
) -> FileCodeManager:
    """
    创建文件编码管理器实例

    Args:
        base_persist_dir: 向量存储基础目录

    Returns:
        文件编码管理器实例
    """
    return FileCodeManager(base_persist_dir=base_persist_dir)


# 使用示例
def usage_example():
    """使用示例"""
    # 创建管理器
    manager = create_file_code_manager()

    # 上传文件
    result = manager.upload_excel_and_get_code("path/to/user_data.xlsx")
    if result["success"]:
        file_code = result["file_code"]
        print(f"文件上传成功，编码: {file_code}")

        # 查看文件信息
        info = manager.get_file_info_by_code(file_code)
        print(f"文件信息: {info}")

        # 列出所有文件
        all_files = manager.list_available_codes()
        print(f"所有文件: {len(all_files)} 个")

        # 获取统计信息
        stats = manager.get_statistics()
        print(f"统计信息: {stats}")
    else:
        print(f"文件上传失败: {result['error']}")


if __name__ == "__main__":
    usage_example()
