"""
OpenAI LLM提供商实现
"""

import logging
from typing import List, Any, Dict, Optional
import time

try:
    from openai import OpenAI

    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    OpenAI = None

from ..base import BaseLLM, LLMConfig, LLMResponse, LLMProvider
from ..base import (
    LLMError,
    LLMConnectionError,
    LLMAuthenticationError,
    LLMRateLimitError,
)
from ..factory import register_llm_provider

logger = logging.getLogger(__name__)


@register_llm_provider(LLMProvider.OPENAI)
class OpenAILLM(BaseLLM):
    """OpenAI LLM实现"""

    def _initialize_client(self):
        """初始化OpenAI客户端"""
        if not OPENAI_AVAILABLE:
            raise LLMError("OpenAI库未安装，请运行: pip install openai")

        try:
            if not self.config.api_key:
                raise LLMAuthenticationError("OpenAI API密钥未设置")

            client_params = {
                "api_key": self.config.api_key,
                "timeout": self.config.timeout,
            }

            # 如果设置了自定义base_url，使用它（支持兼容OpenAI格式的其他服务）
            if self.config.base_url:
                client_params["base_url"] = self.config.base_url

            self._client = OpenAI(**client_params)
            logger.debug(f"OpenAI客户端初始化成功: {self.config.model}")

        except Exception as e:
            logger.error(f"OpenAI客户端初始化失败: {str(e)}")
            raise LLMConnectionError(f"OpenAI客户端初始化失败: {str(e)}") from e

    def invoke(self, prompt: str, **kwargs) -> LLMResponse:
        """
        调用OpenAI生成响应

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Returns:
            LLM响应
        """
        if not self._client:
            raise LLMError("OpenAI客户端未初始化")

        # 构建消息
        messages = [{"role": "user", "content": prompt}]

        # 合并配置参数和调用参数
        call_params = {
            "model": self.config.model,
            "messages": messages,
            **self._merge_call_params(**kwargs),
        }

        # 重试机制
        last_exception = None
        for attempt in range(self.config.max_retries):
            try:
                logger.info(f"OpenAI调用尝试 {attempt + 1}/{self.config.max_retries}")

                # 调用OpenAI
                response = self._client.chat.completions.create(**call_params)

                # 提取响应内容
                content = response.choices[0].message.content

                # 构建响应对象
                llm_response = LLMResponse(
                    content=content,
                    model=response.model,
                    finish_reason=response.choices[0].finish_reason,
                    raw_response=response,
                )

                # 提取使用信息
                if response.usage:
                    llm_response.usage = {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens,
                    }

                logger.info(f"OpenAI调用成功，响应长度: {len(content)}")
                return llm_response

            except Exception as e:
                last_exception = e
                logger.warning(f"OpenAI调用失败 (尝试 {attempt + 1}): {str(e)}")

                # 根据错误类型决定是否重试
                error_str = str(e).lower()
                if "rate limit" in error_str or "429" in error_str:
                    if attempt < self.config.max_retries - 1:
                        wait_time = 2**attempt  # 指数退避
                        logger.info(f"遇到速率限制，等待 {wait_time} 秒后重试")
                        time.sleep(wait_time)
                        continue
                    else:
                        raise LLMRateLimitError(f"OpenAI速率限制: {str(e)}") from e
                elif "authentication" in error_str or "401" in error_str:
                    raise LLMAuthenticationError(f"OpenAI认证失败: {str(e)}") from e
                elif "invalid" in error_str and "request" in error_str:
                    # 无效请求不重试
                    raise LLMError(f"OpenAI无效请求: {str(e)}") from e
                elif attempt < self.config.max_retries - 1:
                    # 其他错误也重试
                    time.sleep(1)
                    continue
                else:
                    break

        # 所有重试都失败了
        raise LLMError(
            f"OpenAI调用失败，已重试 {self.config.max_retries} 次: {str(last_exception)}"
        ) from last_exception

    def batch_invoke(self, prompts: List[str], **kwargs) -> List[LLMResponse]:
        """
        批量调用OpenAI

        Args:
            prompts: 提示列表
            **kwargs: 额外参数

        Returns:
            响应列表
        """
        responses = []
        for i, prompt in enumerate(prompts):
            try:
                logger.debug(f"批量调用OpenAI: {i+1}/{len(prompts)}")
                response = self.invoke(prompt, **kwargs)
                responses.append(response)
            except Exception as e:
                logger.error(f"批量调用第 {i+1} 个提示失败: {str(e)}")
                # 创建错误响应
                error_response = LLMResponse(
                    content=f"调用失败: {str(e)}", model=self.config.model
                )
                responses.append(error_response)

        return responses

    def stream_invoke(self, prompt: str, **kwargs):
        """
        流式调用OpenAI

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Yields:
            流式响应片段
        """
        if not self._client:
            raise LLMError("OpenAI客户端未初始化")

        try:
            # 构建消息
            messages = [{"role": "user", "content": prompt}]

            # 合并配置参数和调用参数
            call_params = {
                "model": self.config.model,
                "messages": messages,
                "stream": True,
                **self._merge_call_params(**kwargs),
            }

            logger.debug("开始OpenAI流式调用")

            # OpenAI流式调用
            stream = self._client.chat.completions.create(**call_params)
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            logger.error(f"OpenAI流式调用失败: {str(e)}")
            raise LLMError(f"OpenAI流式调用失败: {str(e)}") from e
