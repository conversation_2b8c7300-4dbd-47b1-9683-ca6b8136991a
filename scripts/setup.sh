#!/bin/bash

# Deep Risk RAG 系统设置脚本

set -e

echo "🚀 开始设置 Deep Risk RAG 微服务系统..."

# 检查Docker和Docker Compose
echo "📋 检查系统依赖..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查NVIDIA Docker (用于GPU支持)
if command -v nvidia-docker &> /dev/null; then
    echo "✅ NVIDIA Docker 已安装，支持GPU加速"
else
    echo "⚠️  NVIDIA Docker 未安装，向量服务将使用CPU模式"
fi

# 创建必要的目录
echo "📁 创建项目目录..."
mkdir -p cache/chroma_data
mkdir -p cache/models
mkdir -p data/uploads
mkdir -p logs

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，设置必要的API密钥"
fi

# 检查模型文件
echo "🤖 检查嵌入模型..."
if [ ! -d "models/bge-m3-safetensors-only" ]; then
    echo "📥 下载BGE-M3模型..."
    python models/download_models.py
else
    echo "✅ BGE-M3模型已存在"
fi

# 构建Docker镜像
echo "🔨 构建Docker镜像..."
cd docker
docker-compose build

echo "✅ 系统设置完成！"
echo ""
echo "📖 下一步操作："
echo "1. 编辑 .env 文件，设置API密钥"
echo "2. 运行 'scripts/start-services.sh' 启动服务"
echo "3. 访问 http://localhost:8002/docs 查看向量服务API文档"
echo "4. 访问 http://localhost:8003/docs 查看分析服务API文档"
