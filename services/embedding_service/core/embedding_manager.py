"""
嵌入管理器
负责BGE-M3模型的加载、管理和嵌入计算
"""

import asyncio
import gc
import time
import hashlib
from typing import List, Dict, Any, Optional
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import get_logger
from services.embedding_service.config import config
from services.embedding_service.core.bge_embedding import BGEEmbeddingService
from services.embedding_service.core.cache_manager import CacheManager

logger = get_logger(__name__)


class EmbeddingManager:
    """嵌入管理器 - 统一管理BGE-M3嵌入服务"""
    
    def __init__(self):
        """初始化嵌入管理器"""
        self.embedding_service: Optional[BGEEmbeddingService] = None
        self.cache_manager: Optional[CacheManager] = None
        self.is_initialized = False
        self.initialization_lock = asyncio.Lock()
        
        # 性能统计
        self.stats = {
            "total_requests": 0,
            "total_documents": 0,
            "total_processing_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
        }
        
        logger.info("嵌入管理器已创建")
    
    async def initialize(self):
        """初始化嵌入服务"""
        async with self.initialization_lock:
            if self.is_initialized:
                logger.info("嵌入管理器已初始化，跳过重复初始化")
                return
            
            try:
                logger.info("开始初始化嵌入管理器...")
                
                # 初始化缓存管理器
                if config.enable_cache:
                    logger.info("初始化缓存管理器...")
                    self.cache_manager = CacheManager(
                        max_size=config.max_cache_size,
                        ttl=config.cache_ttl
                    )
                
                # 初始化BGE嵌入服务
                logger.info("初始化BGE-M3嵌入服务...")
                self.embedding_service = BGEEmbeddingService(
                    model_name=config.model_name,
                    device=config.device,
                    batch_size=config.batch_size,
                    max_length=config.max_length,
                    normalize_embeddings=config.normalize_embeddings,
                    enable_cache=False,  # 使用统一的缓存管理器
                    auto_cleanup=config.auto_cleanup,
                    memory_monitor=config.enable_memory_monitoring,
                )
                
                # 加载模型
                await asyncio.get_event_loop().run_in_executor(
                    None, self.embedding_service._load_model
                )
                
                self.is_initialized = True
                logger.info("✅ 嵌入管理器初始化完成")
                
            except Exception as e:
                logger.error(f"❌ 嵌入管理器初始化失败: {e}")
                await self.cleanup()
                raise
    
    async def is_ready(self) -> bool:
        """检查服务是否准备就绪"""
        return (
            self.is_initialized and 
            self.embedding_service is not None and 
            self.embedding_service.model is not None
        )
    
    async def embed_documents(
        self, 
        texts: List[str], 
        normalize: bool = True
    ) -> List[List[float]]:
        """
        批量文档嵌入
        
        Args:
            texts: 文本列表
            normalize: 是否标准化
            
        Returns:
            嵌入向量列表
        """
        if not await self.is_ready():
            raise RuntimeError("嵌入服务未准备就绪")
        
        start_time = time.time()
        
        try:
            # 检查缓存
            cached_embeddings = []
            uncached_texts = []
            uncached_indices = []
            
            if self.cache_manager:
                for i, text in enumerate(texts):
                    cache_key = self._get_cache_key(text, normalize)
                    cached_embedding = await self.cache_manager.get(cache_key)
                    
                    if cached_embedding is not None:
                        cached_embeddings.append((i, cached_embedding))
                        self.stats["cache_hits"] += 1
                    else:
                        uncached_texts.append(text)
                        uncached_indices.append(i)
                        self.stats["cache_misses"] += 1
            else:
                uncached_texts = texts
                uncached_indices = list(range(len(texts)))
            
            # 计算未缓存的嵌入
            new_embeddings = []
            if uncached_texts:
                logger.debug(f"计算 {len(uncached_texts)} 个未缓存的嵌入")
                
                # 在线程池中执行嵌入计算
                new_embeddings = await asyncio.get_event_loop().run_in_executor(
                    None, 
                    self.embedding_service.embed_documents,
                    uncached_texts
                )
                
                # 缓存新的嵌入
                if self.cache_manager:
                    for text, embedding in zip(uncached_texts, new_embeddings):
                        cache_key = self._get_cache_key(text, normalize)
                        await self.cache_manager.set(cache_key, embedding)
            
            # 合并结果
            final_embeddings = [None] * len(texts)
            
            # 填入缓存的结果
            for i, embedding in cached_embeddings:
                final_embeddings[i] = embedding
            
            # 填入新计算的结果
            for i, embedding in zip(uncached_indices, new_embeddings):
                final_embeddings[i] = embedding
            
            # 更新统计
            processing_time = time.time() - start_time
            self.stats["total_requests"] += 1
            self.stats["total_documents"] += len(texts)
            self.stats["total_processing_time"] += processing_time
            
            logger.debug(f"批量嵌入完成: {len(texts)} 个文档，耗时 {processing_time:.3f}秒")
            
            return final_embeddings
            
        except Exception as e:
            logger.error(f"批量文档嵌入失败: {e}")
            raise
    
    async def embed_query(self, text: str, normalize: bool = True) -> List[float]:
        """
        单个查询嵌入
        
        Args:
            text: 查询文本
            normalize: 是否标准化
            
        Returns:
            嵌入向量
        """
        embeddings = await self.embed_documents([text], normalize)
        return embeddings[0] if embeddings else []
    
    def _get_cache_key(self, text: str, normalize: bool) -> str:
        """生成缓存键"""
        content = f"{text}|{normalize}|{config.model_name}"
        return hashlib.md5(content.encode()).hexdigest()
    
    async def get_model_status(self) -> Dict[str, Any]:
        """获取模型状态"""
        if not self.embedding_service:
            return {"status": "not_initialized"}
        
        return {
            "status": "loaded" if self.embedding_service.model else "not_loaded",
            "model_name": config.model_name,
            "device": config.device,
            "batch_size": config.batch_size,
            "max_length": config.max_length,
            "memory_usage": self.embedding_service.get_memory_usage() if hasattr(self.embedding_service, 'get_memory_usage') else {}
        }
    
    async def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        status = await self.get_model_status()
        
        return {
            "model_name": config.model_name,
            "dimension": 1024,  # BGE-M3固定维度
            "max_length": config.max_length,
            "device": config.device,
            "status": status["status"],
            "memory_usage": status.get("memory_usage", {}),
            "stats": self.stats.copy(),
            "cache_enabled": config.enable_cache,
            "cache_size": await self.cache_manager.size() if self.cache_manager else 0,
        }
    
    async def reload_model(self):
        """重新加载模型"""
        if not self.embedding_service:
            raise RuntimeError("嵌入服务未初始化")
        
        logger.info("开始重新加载模型...")
        
        # 在线程池中执行模型重载
        await asyncio.get_event_loop().run_in_executor(
            None, self.embedding_service.reload_model
        )
        
        logger.info("模型重新加载完成")
    
    async def clear_cache(self):
        """清理缓存"""
        if self.cache_manager:
            await self.cache_manager.clear()
            logger.info("缓存已清理")
        else:
            logger.info("缓存未启用，无需清理")
    
    async def cleanup(self):
        """清理资源"""
        logger.info("开始清理嵌入管理器资源...")
        
        try:
            # 清理缓存
            if self.cache_manager:
                await self.cache_manager.clear()
                self.cache_manager = None
            
            # 清理嵌入服务
            if self.embedding_service:
                # 在线程池中执行清理
                await asyncio.get_event_loop().run_in_executor(
                    None, self.embedding_service.cleanup_memory, True
                )
                self.embedding_service = None
            
            self.is_initialized = False
            
            # 强制垃圾回收
            gc.collect()
            
            logger.info("✅ 嵌入管理器资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 嵌入管理器资源清理失败: {e}")
            raise
