"""
文件处理器
负责文件上传、解析和文档分块
"""

import os
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import hashlib

from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

from shared.utils.logger import get_logger
from shared.utils.file_utils import calculate_file_hash, get_file_info
from shared.models.file_info import FileInfo, FileStatus
from services.vector_service.config import config

logger = get_logger(__name__)


class FileProcessor:
    """文件处理器"""

    def __init__(self, upload_dir: str = None):
        """
        初始化文件处理器

        Args:
            upload_dir: 上传目录
        """
        self.upload_dir = Path(upload_dir or config.upload_dir)
        self.upload_dir.mkdir(parents=True, exist_ok=True)

        # 文档分割器
        self.text_splitter = RecursiveCharacterTextSplitter.from_language(
            language="markdown",
            chunk_size=config.chunk_size,
            chunk_overlap=config.chunk_overlap,
        )

        logger.info(f"File processor initialized, upload dir: {self.upload_dir}")

    def save_uploaded_file(
        self, file_data: bytes, filename: str
    ) -> Tuple[str, FileInfo]:
        """
        保存上传的文件

        Args:
            file_data: 文件数据
            filename: 文件名

        Returns:
            (文件路径, 文件信息)
        """
        # 检查文件扩展名
        file_path = Path(filename)
        if file_path.suffix.lower() not in config.allowed_extensions:
            raise ValueError(f"Unsupported file type: {file_path.suffix}")

        # 检查文件大小
        file_size = len(file_data)
        max_size_bytes = config.max_file_size * 1024 * 1024  # MB to bytes
        if file_size > max_size_bytes:
            raise ValueError(
                f"File too large: {file_size} bytes > {max_size_bytes} bytes"
            )

        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        content_hash = hashlib.md5(file_data).hexdigest()[:8]
        safe_filename = f"{file_path.stem}_{timestamp}_{content_hash}{file_path.suffix}"

        # 保存文件
        saved_path = self.upload_dir / safe_filename
        with open(saved_path, "wb") as f:
            f.write(file_data)

        # 生成文件编码
        file_code = f"{file_path.stem}_{timestamp}_{content_hash}"

        # 创建文件信息
        file_info = FileInfo(
            file_code=file_code,
            file_name=filename,
            file_path=str(saved_path),
            file_size=file_size,
            file_type=file_path.suffix.lower(),
            content_hash=content_hash,
            status=FileStatus.UPLOADING,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        logger.info(f"File saved: {filename} -> {safe_filename} (code: {file_code})")
        return str(saved_path), file_info

    def process_csv_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理CSV文件 - 基于原有CSV处理逻辑

        Args:
            file_path: 文件路径
            file_code: 文件编码

        Returns:
            文档列表
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding="utf-8")
            logger.info(f"CSV文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 使用原有的分块逻辑创建文档
            documents = self._create_chunked_documents_from_df(df, file_path, file_code)

            logger.info(f"✅ CSV文件处理完成，总共创建了 {len(documents)} 个文档块")
            return documents

        except Exception as e:
            logger.error(f"处理CSV文件失败: {e}")
            raise

    def process_excel_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理Excel文件 - 基于原有逻辑

        Args:
            file_path: 文件路径
            file_code: 文件编码

        Returns:
            文档列表
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logger.info(f"Excel文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 使用相同的分块逻辑
            documents = self._create_chunked_documents_from_df(df, file_path, file_code)

            logger.info(f"✅ Excel文件处理完成，总共创建了 {len(documents)} 个文档块")
            return documents

        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            raise

    def _convert_df_to_text(self, df: pd.DataFrame, file_code: str) -> str:
        """
        将DataFrame转换为文本格式 - 基于原有CSV处理逻辑

        Args:
            df: DataFrame
            file_code: 文件编码

        Returns:
            格式化的文本内容
        """
        content_parts = [
            "===用户风控数据===",
            f"数据维度: {len(df)} 行 x {len(df.columns)} 列",
            f"数据列: {', '.join(df.columns.tolist())}",
            "",
            "===详细数据===",
            "字段名称 | 字段值",
            "-" * 50,
        ]

        # 逐行添加数据
        for idx, (_, row) in enumerate(df.iterrows()):
            content_parts.append(f"\n---第{idx + 1}条记录---")
            for col in df.columns:
                value = row[col]
                if pd.isna(value):
                    value = "空值"
                else:
                    value = str(value)
                content_parts.append(f"{col}: {value}")

        # 添加数值列统计
        numeric_cols = df.select_dtypes(include=["number"]).columns
        if len(numeric_cols) > 0:
            content_parts.extend(["\n===数值字段统计==="])
            for col in numeric_cols:
                if not df[col].empty:
                    stats = df[col].describe()
                    content_parts.append(
                        f"{col}: 均值={stats['mean']:.2f}, 最大值={stats['max']}, 最小值={stats['min']}, 标准差={stats['std']:.2f}"
                    )

        return "\n".join(content_parts)

    def _create_chunked_documents_from_df(
        self, df: pd.DataFrame, file_path: str, file_code: str
    ) -> List[Document]:
        """
        基于原有逻辑创建分块文档

        Args:
            df: DataFrame数据
            file_path: 文件路径
            file_code: 文件编码

        Returns:
            分块文档列表
        """
        documents = []
        total_rows = len(df)
        rows_per_chunk = 50  # 每个文档块包含的行数
        chunk_overlap_rows = 5  # 分块重叠的行数

        # 计算需要的块数
        num_chunks = (total_rows + rows_per_chunk - 1) // rows_per_chunk
        logger.info(f"将 {total_rows} 行数据分为 {num_chunks} 个文档块")

        for chunk_id in range(num_chunks):
            # 计算当前块的行范围
            start_row = chunk_id * rows_per_chunk
            end_row = min(start_row + rows_per_chunk, total_rows)

            # 添加重叠行（除了第一块）
            if chunk_id > 0:
                overlap_start = max(0, start_row - chunk_overlap_rows)
                chunk_df = df.iloc[overlap_start:end_row]
                actual_start = overlap_start
            else:
                chunk_df = df.iloc[start_row:end_row]
                actual_start = start_row

            # 生成块内容
            chunk_content = self._format_chunk_content(
                chunk_df, chunk_id, actual_start, end_row - 1, total_rows, file_code
            )

            # 创建文档
            doc = Document(
                page_content=chunk_content,
                metadata={
                    "source": str(file_path),
                    "file_name": Path(file_path).name,
                    "file_code": file_code,
                    "chunk_id": chunk_id,
                    "chunk_start_row": actual_start,
                    "chunk_end_row": end_row - 1,
                    "total_rows": total_rows,
                    "data_type": "csv_risk_data",
                    "created_at": datetime.now().isoformat(),
                    "content_size": len(chunk_content),
                },
            )

            documents.append(doc)
            logger.debug(f"创建文档块 {chunk_id}: 行 {actual_start}-{end_row-1}")

        return documents

    def _format_chunk_content(
        self,
        chunk_df: pd.DataFrame,
        chunk_id: int,
        start_row: int,
        end_row: int,
        total_rows: int,
        file_code: str,
    ) -> str:
        """
        格式化块内容

        Args:
            chunk_df: 块数据框
            chunk_id: 块ID
            start_row: 开始行
            end_row: 结束行
            total_rows: 总行数
            file_code: 文件编码

        Returns:
            格式化的内容
        """
        content_parts = [
            f"===风控数据块 {chunk_id + 1}===",
            f"文件编码: {file_code}",
            f"数据范围: 第{start_row + 1}-{end_row + 1}行 (共{total_rows}行)",
            f"块大小: {len(chunk_df)} 行 x {len(chunk_df.columns)} 列",
            f"数据列: {', '.join(chunk_df.columns.tolist())}",
            "",
            "===块数据内容===",
        ]

        # 添加数据行
        for idx, (_, row) in enumerate(chunk_df.iterrows()):
            content_parts.append(f"\n---记录 {start_row + idx + 1}---")
            for col in chunk_df.columns:
                value = row[col]
                if pd.isna(value):
                    value = "空值"
                else:
                    value = str(value)
                content_parts.append(f"{col}: {value}")

        return "\n".join(content_parts)

    def process_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理文件（自动识别类型）

        Args:
            file_path: 文件路径
            file_code: 文件编码

        Returns:
            文档列表
        """
        file_path = Path(file_path)
        file_ext = file_path.suffix.lower()

        if file_ext == ".csv":
            return self.process_csv_file(str(file_path), file_code)
        elif file_ext in [".xlsx", ".xls"]:
            return self.process_excel_file(str(file_path), file_code)
        else:
            raise ValueError(f"Unsupported file type: {file_ext}")

    def cleanup_file(self, file_path: str) -> bool:
        """
        清理临时文件

        Args:
            file_path: 文件路径

        Returns:
            是否清理成功
        """
        try:
            file_path = Path(file_path)
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Cleaned up file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to cleanup file {file_path}: {e}")
            return False
