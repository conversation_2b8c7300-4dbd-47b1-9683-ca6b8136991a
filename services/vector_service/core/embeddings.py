"""
BGE-M3嵌入服务
基于原有的embeddings.py进行适配，保持完全一致的实现
"""

import os
import time
import hashlib
import pickle
import torch
import gc
from typing import List, Optional, Dict, Any
from pathlib import Path
import numpy as np
from sklearn.preprocessing import normalize
from FlagEmbedding import BGEM3FlagModel

from shared.utils.logger import get_logger
from services.vector_service.config import config

logger = get_logger(__name__)


def batch_process(items: List[Any], batch_size: int):
    """批处理工具函数"""
    for i in range(0, len(items), batch_size):
        yield items[i:i + batch_size]


def get_optimal_device(device: str = "auto") -> str:
    """获取最优设备"""
    if device == "auto":
        if torch.cuda.is_available():
            return "cuda"
        else:
            return "cpu"
    return device


def recommend_device_settings(model_size_gb: float = 2.3) -> Dict[str, Any]:
    """推荐设备设置"""
    settings = {
        "use_fp16": torch.cuda.is_available(),
        "batch_size": 32,
        "warnings": []
    }

    if torch.cuda.is_available():
        # GPU可用时的推荐设置
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        if gpu_memory < 8:
            settings["batch_size"] = 16
            settings["warnings"].append("GPU内存较小，建议使用较小的batch_size")
    else:
        settings["use_fp16"] = False
        settings["batch_size"] = 16
        settings["warnings"].append("未检测到GPU，使用CPU模式")

    return settings


class BGEEmbeddingService:
    """BGE-M3嵌入服务 - 基于原有BGEM3Embeddings实现"""

    def __init__(
        self,
        model_name: str = None,
        cache_dir: Optional[str] = None,
        batch_size: int = None,
        max_length: int = None,
        device: str = "auto",
        enable_cache: bool = True,
        cache_ttl: int = 86400,  # 24小时
        normalize_embeddings: bool = True,
        auto_cleanup: bool = True,  # 自动清理开关
        memory_monitor: bool = True,  # 显存监控开关
        **kwargs,
    ):
        """
        初始化BGE-M3嵌入服务

        Args:
            model_name: 模型名称
            cache_dir: 模型缓存目录
            batch_size: 批处理大小
            max_length: 最大序列长度
            device: 设备 (cpu, cuda, auto)
            enable_cache: 是否启用嵌入缓存
            cache_ttl: 缓存过期时间（秒）
            normalize_embeddings: 是否标准化嵌入向量
            auto_cleanup: 是否启用自动显存清理
            memory_monitor: 是否启用显存监控
        """
        # 使用配置或默认值
        self.model_name = model_name or config.model_name
        self.cache_dir = Path(cache_dir) if cache_dir else Path(config.model_cache_dir)
        self.batch_size = batch_size or config.batch_size
        self.max_length = max_length or config.max_length
        self.enable_cache = enable_cache
        self.cache_ttl = cache_ttl
        self.normalize_embeddings = normalize_embeddings
        
        # GPU显存管理配置
        self.auto_cleanup = auto_cleanup if auto_cleanup is not None else config.auto_cleanup
        self.memory_monitor = memory_monitor if memory_monitor is not None else config.memory_monitor
        self.cleanup_threshold = config.cleanup_threshold
        self.batch_cleanup_interval = config.batch_cleanup_interval

        # 使用统一的设备选择逻辑
        self.device = get_optimal_device(device)

        # 获取设备推荐设置
        device_recommendations = recommend_device_settings(model_size_gb=2.3)
        self.use_fp16 = device_recommendations.get("use_fp16", False)

        # 如果用户没有指定批处理大小，使用推荐值
        if batch_size is None:
            self.batch_size = device_recommendations.get("batch_size", 32)

        # 输出设备推荐警告
        for warning in device_recommendations.get("warnings", []):
            logger.warning(warning)

        # 初始化模型
        self.model = None
        self._load_model()

        # 初始化缓存
        self.embedding_cache = {}
        self.cache_dir_path = self.cache_dir / "embedding_cache"
        if self.enable_cache:
            self.cache_dir_path.mkdir(parents=True, exist_ok=True)
            self._load_cache()

        # 周期性清理统计
        self._cleanup_counter = 0
        self._periodic_cleanup_interval = 100  # 每100次嵌入操作进行一次周期性清理

        logger.info(f"BGE-M3 embedding service initialized on {self.device}")
        if self.memory_monitor:
            self.log_memory_usage("初始化完成")

    def _load_model(self):
        """加载BGE-M3嵌入模型 - 仅从本地路径加载"""
        try:
            logger.info(f"正在加载 BGE-M3 嵌入模型")
            logger.info(f"本地模型路径: {self.model_name}")
            logger.info(f"设备: {self.device}")
            logger.info(f"缓存目录: {self.cache_dir}")

            # 验证本地模型路径存在
            model_path = Path(self.model_name)
            if not model_path.exists():
                raise FileNotFoundError(f"本地模型路径不存在: {model_path}")
            
            # 检查必要的模型文件
            required_files = ["config.json", "model.safetensors", "tokenizer.json"]
            missing_files = []
            for file_name in required_files:
                if not (model_path / file_name).exists():
                    missing_files.append(file_name)
            
            if missing_files:
                raise FileNotFoundError(f"缺少必要的模型文件: {missing_files}")

            logger.info(f"✅ 本地模型文件验证通过")

            # 加载BGE-M3模型，使用推荐的设置
            self.model = BGEM3FlagModel(
                str(model_path), use_fp16=self.use_fp16, device=self.device
            )

            logger.info("✅ BGE-M3 模型加载成功")
            logger.info("模型维度: 1024")  # BGE-M3固定维度
            logger.info(f"最大序列长度: {self.max_length}")
            logger.info(f"使用半精度: {self.use_fp16}")
            logger.info(f"设备: {self.device}")

        except Exception as e:
            logger.error(f"❌ BGE-M3 模型加载失败: {e}")

            # 模型加载失败时清理可能的部分加载状态
            if hasattr(self, 'model'):
                self.model = None
                
            # 清理可能占用的显存
            gc.collect()
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.empty_cache()

            # 提供本地模型加载的故障排除建议
            error_msg = f"无法从本地路径加载 BGE-M3 嵌入模型: {e}\n\n"
            error_msg += "故障排除建议:\n"
            error_msg += "1. 确保已安装 FlagEmbedding 库: pip install FlagEmbedding\n"
            error_msg += "2. 检查本地模型路径是否正确\n"
            error_msg += "3. 验证模型文件完整性（config.json, model.safetensors, tokenizer.json）\n"
            error_msg += "4. 检查磁盘空间，BGE-M3 需要约 2.3GB 空间\n"
            error_msg += f"5. 当前模型路径: {self.model_name}\n"

            if "CUDA" in str(e) or "cuda" in str(e):
                error_msg += (
                    "6. CUDA 相关错误：尝试设置 device='cpu' 或检查 CUDA 安装\n"
                )

            if "memory" in str(e).lower():
                error_msg += (
                    "6. 内存不足：尝试设置 use_fp16=True 或使用更小的 batch_size\n"
                )

            raise RuntimeError(error_msg)

    def _get_cache_key(self, text: str) -> str:
        """生成BGE-M3特定的缓存键"""
        # 使用文本内容、模型名称、序列长度和标准化设置生成唯一键
        content = f"BGE-M3:{self.model_name}:{self.max_length}:{self.normalize_embeddings}:{text}"
        return hashlib.md5(content.encode("utf-8")).hexdigest()

    def _load_cache(self):
        """加载缓存"""
        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            if cache_file.exists():
                with open(cache_file, "rb") as f:
                    cache_data = pickle.load(f)

                # 检查缓存是否过期
                current_time = time.time()
                valid_cache = {}

                for key, (embedding, timestamp) in cache_data.items():
                    if current_time - timestamp < self.cache_ttl:
                        valid_cache[key] = (embedding, timestamp)

                self.embedding_cache = valid_cache
                logger.info(f"加载嵌入缓存: {len(valid_cache)} 条记录")

        except Exception as e:
            logger.warning(f"加载缓存失败: {e}")
            self.embedding_cache = {}

    def _save_cache(self):
        """保存缓存"""
        if not self.enable_cache:
            return

        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            with open(cache_file, "wb") as f:
                pickle.dump(self.embedding_cache, f)
        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")

    def _get_cached_embedding(self, text: str) -> Optional[np.ndarray]:
        """获取缓存的嵌入"""
        if not self.enable_cache:
            return None

        cache_key = self._get_cache_key(text)
        if cache_key in self.embedding_cache:
            embedding, timestamp = self.embedding_cache[cache_key]

            # 检查是否过期
            if time.time() - timestamp < self.cache_ttl:
                return embedding
            else:
                # 删除过期缓存
                del self.embedding_cache[cache_key]

        return None

    def _cache_embedding(self, text: str, embedding: np.ndarray):
        """缓存嵌入"""
        if not self.enable_cache:
            return

        cache_key = self._get_cache_key(text)
        self.embedding_cache[cache_key] = (embedding, time.time())

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        if not text or not text.strip():
            return ""

        # 基本清理
        text = text.strip()

        # 限制长度（按字符数估算，BGE-M3支持8192 tokens）
        max_chars = self.max_length * 4  # 粗略估算
        if len(text) > max_chars:
            text = text[:max_chars]
            logger.debug(f"Text truncated to {max_chars} characters")

        return text

    def get_memory_info(self) -> Dict[str, Any]:
        """
        获取GPU显存使用信息（高精度版本）
        
        Returns:
            显存信息字典
        """
        memory_info = {
            "device": self.device,
            "cuda_available": torch.cuda.is_available()
        }
        
        if torch.cuda.is_available() and self.device == "cuda":
            # ★★★ 新增：先同步确保准确读取 ★★★
            torch.cuda.synchronize()
            
            # 当前设备显存信息
            current_device = torch.cuda.current_device()
            
            # ★★★ 新增：多次读取取平均值提高精度 ★★★
            allocated_values = []
            reserved_values = []
            for _ in range(3):
                allocated_values.append(torch.cuda.memory_allocated(current_device))
                reserved_values.append(torch.cuda.memory_reserved(current_device))
                torch.cuda.synchronize()
            
            allocated = sum(allocated_values) / len(allocated_values) / 1024**3  # GB
            reserved = sum(reserved_values) / len(reserved_values) / 1024**3  # GB
            
            memory_info.update({
                "current_device": current_device,
                "allocated": allocated,
                "reserved": reserved,
                "max_allocated": torch.cuda.max_memory_allocated(current_device) / 1024**3,  # GB
                "max_reserved": torch.cuda.max_memory_reserved(current_device) / 1024**3,  # GB
            })
            
            # 获取设备属性
            device_props = torch.cuda.get_device_properties(current_device)
            memory_info.update({
                "total_memory": device_props.total_memory / 1024**3,  # GB
                "device_name": device_props.name,
                "memory_usage_percent": (allocated / (device_props.total_memory / 1024**3)) * 100
            })
        else:
            # CPU模式或CUDA不可用时的信息
            import psutil
            try:
                # 获取系统内存信息
                ram_info = psutil.virtual_memory()
                memory_info.update({
                    "mode": "CPU",
                    "system_ram_total": ram_info.total / 1024**3,  # GB
                    "system_ram_available": ram_info.available / 1024**3,  # GB
                    "system_ram_used": ram_info.used / 1024**3,  # GB
                    "system_ram_percent": ram_info.percent,
                    "memory_usage_percent": ram_info.percent  # 兼容性字段
                })
            except ImportError:
                # 如果psutil不可用，提供基本信息
                memory_info.update({
                    "mode": "CPU",
                    "note": "psutil not available, limited memory info",
                    "memory_usage_percent": 0  # 兼容性字段
                })
        
        return memory_info

    def log_memory_usage(self, context: str = ""):
        """
        记录显存使用情况
        
        Args:
            context: 上下文信息
        """
        if not self.memory_monitor:
            return
            
        try:
            memory_info = self.get_memory_info()
            if memory_info["cuda_available"] and self.device == "cuda":
                allocated = memory_info["allocated"]
                total = memory_info["total_memory"]
                usage_percent = memory_info["memory_usage_percent"]
                
                logger.info(
                    f"🔍 GPU显存使用 [{context}]: "
                    f"{allocated:.2f}GB/{total:.2f}GB ({usage_percent:.1f}%)"
                )
                
                # 如果显存使用率过高，发出警告
                if usage_percent > self.cleanup_threshold:
                    logger.warning(
                        f"⚠️  GPU显存使用率过高: {usage_percent:.1f}% (阈值: {self.cleanup_threshold}%), "
                        f"建议进行显存清理"
                    )
            else:
                # CPU模式下的内存监控
                logger.debug(f"💻 CPU模式运行 [{context}]: 设备={self.device}")
        except Exception as e:
            logger.debug(f"显存监控失败: {e}")

    def cleanup_memory(self, force: bool = False):
        """
        清理GPU显存
        
        Args:
            force: 是否强制清理（包括模型）
        """
        try:
            if self.memory_monitor:
                self.log_memory_usage("清理前")
            
            # ★★★ 新增：先同步所有GPU操作 ★★★
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.synchronize()
            
            # 清理Python垃圾回收（适用于所有环境）
            gc.collect()
            
            # 清理CUDA缓存（如果使用CUDA）
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.empty_cache()
                # ★★★ 新增：重置峰值内存统计 ★★★
                torch.cuda.reset_max_memory_allocated()
                torch.cuda.synchronize()  # 确保清理操作完成
                logger.debug("✅ CUDA缓存已清理并同步")
            else:
                logger.debug("💻 CPU模式: 执行Python垃圾回收")
            
            # 强制清理时，清理模型引用
            if force and self.model is not None:
                logger.info("🔄 强制清理模型引用...")
                del self.model
                self.model = None
                gc.collect()
                if torch.cuda.is_available() and self.device == "cuda":
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                logger.info("✅ 模型引用已清理")
            
            # ★★★ 新增：延迟一小段时间确保清理生效 ★★★
            import time
            time.sleep(0.1)  # 100ms延迟确保GPU操作完成
            
            if self.memory_monitor:
                self.log_memory_usage("清理后")
                
        except Exception as e:
            logger.warning(f"显存清理时出现警告: {e}")

    def synchronize_and_cleanup(self):
        """
        同步GPU操作并清理显存
        
        专门用于确保GPU操作完成后进行彻底清理
        """
        try:
            if torch.cuda.is_available() and self.device == "cuda":
                # 强制同步所有GPU操作
                torch.cuda.synchronize()
                logger.debug("🔄 GPU操作已同步")
            
            # 立即清理
            gc.collect()
            
            if torch.cuda.is_available() and self.device == "cuda":
                torch.cuda.empty_cache()
                torch.cuda.synchronize()  # 再次同步确保清理完成
                logger.debug("✅ 同步清理完成")
            
        except Exception as e:
            logger.debug(f"同步清理时出现警告: {e}")

    def cleanup(self):
        """
        完整清理资源（服务关闭时调用）
        """
        logger.info("🔄 开始清理BGE嵌入服务资源...")
        
        try:
            # 清理嵌入缓存
            if hasattr(self, 'embedding_cache'):
                self.embedding_cache.clear()
                logger.debug("✅ 嵌入缓存已清理")
            
            # 清理模型
            if hasattr(self, 'model') and self.model is not None:
                del self.model
                self.model = None
                logger.debug("✅ 模型引用已清理")
            
            # 强制清理GPU显存
            self.cleanup_memory(force=True)
            
            # 额外的环境特定清理
            if torch.cuda.is_available() and self.device == "cuda":
                logger.info("✅ BGE嵌入服务资源清理完成 (GPU模式)")
            else:
                logger.info("✅ BGE嵌入服务资源清理完成 (CPU模式)")
            
        except Exception as e:
            logger.warning(f"⚠️  BGE嵌入服务清理时出现警告: {e}")

    def is_model_loaded(self) -> bool:
        """
        检查模型是否已加载
        
        Returns:
            模型是否已加载
        """
        return self.model is not None

    def reload_model(self):
        """
        重新加载模型
        """
        logger.info("🔄 重新加载BGE-M3模型...")
        
        # 先清理现有模型
        if self.model is not None:
            del self.model
            self.model = None
            self.cleanup_memory()
        
        # 重新加载模型
        self._load_model()
        
        if self.memory_monitor:
            self.log_memory_usage("模型重载完成")

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        嵌入文档列表 - 完全基于原有BGEM3Embeddings实现

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表
        """
        if not texts:
            return []

        if self.model is None:
            raise RuntimeError("模型未加载")

        if self.memory_monitor:
            self.log_memory_usage("开始嵌入")

        # ★★★ 优化点 1: 预处理和缓存检查在GPU计算之外 ★★★
        embeddings = [None] * len(texts)
        texts_to_embed = []
        indices_to_embed = []

        # 预处理和缓存查找
        for i, text in enumerate(texts):
            processed_text = self._preprocess_text(text)
            if not processed_text:
                embeddings[i] = [0.0] * self.get_embedding_dimension()
                continue

            # 检查缓存
            cached_embedding = self._get_cached_embedding(processed_text)
            if cached_embedding is not None:
                embeddings[i] = cached_embedding.tolist()
            else:
                texts_to_embed.append(processed_text)
                indices_to_embed.append(i)

        # 如果有需要嵌入的文本，进行批处理
        if texts_to_embed:
            try:
                # ★★★ 关键优化：使用torch.no_grad()减少显存占用 ★★★
                with torch.no_grad():
                    logger.debug(f"正在嵌入 {len(texts_to_embed)} 个新文本")
                    
                    new_embeddings_list = []
                    should_cleanup = False
                    batch_count = 0

                    # 批处理嵌入
                    for batch in batch_process(texts_to_embed, self.batch_size):
                        batch_count += 1
                        
                        try:
                            # 生成嵌入向量
                            batch_output = self.model.encode(
                                batch,
                                batch_size=len(batch),
                                max_length=self.max_length,
                                return_dense=True,
                                return_sparse=False,
                                return_colbert_vecs=False,
                            )
                            
                            # 提取密集嵌入向量
                            batch_embeddings = batch_output["dense_vecs"]
                            
                            # 立即转换为numpy数组并释放GPU张量
                            if hasattr(batch_embeddings, 'cpu'):
                                batch_embeddings = batch_embeddings.cpu().numpy()
                            elif hasattr(batch_embeddings, 'detach'):
                                batch_embeddings = batch_embeddings.detach().cpu().numpy()
                            
                            # 确保是numpy数组格式
                            batch_embeddings = np.array(batch_embeddings, dtype=np.float32)
                            
                            # 手动标准化（如果启用）
                            if self.normalize_embeddings:
                                # 使用 L2 标准化
                                batch_embeddings = normalize(batch_embeddings, norm='l2', axis=1)
                            
                            new_embeddings_list.extend(batch_embeddings)
                            
                            # ★★★ 新增：立即清理临时变量和同步GPU ★★★
                            del batch_embeddings
                            gc.collect()
                            if torch.cuda.is_available() and self.device == "cuda":
                                torch.cuda.empty_cache()
                                torch.cuda.synchronize()  # 确保GPU操作完成
                            
                        except Exception as batch_error:
                            logger.error(f"批处理嵌入失败: {batch_error}")
                            raise

                        # 智能清理策略
                        if self.auto_cleanup:
                            # 每处理固定间隔进行清理
                            if batch_count % self.batch_cleanup_interval == 0:
                                should_cleanup = True
                            
                            # 如果启用监控，也基于显存使用率进行清理
                            elif self.memory_monitor and torch.cuda.is_available() and self.device == "cuda":
                                try:
                                    # ★★★ 新增：同步后再检查显存 ★★★
                                    torch.cuda.synchronize()
                                    memory_info = self.get_memory_info()
                                    usage_percent = memory_info.get("memory_usage_percent", 0)
                                    if usage_percent > self.cleanup_threshold * 0.8:  # 提前清理阈值
                                        should_cleanup = True
                                except:
                                    pass  # 忽略监控错误，继续处理
                        
                        if should_cleanup:
                            gc.collect()
                            if torch.cuda.is_available() and self.device == "cuda":
                                torch.cuda.empty_cache()
                                torch.cuda.synchronize()  # 确保清理操作完成
                            logger.debug(f"批处理清理 - 批次 {batch_count}")

                    # ★★★ 新增：最终同步确保所有GPU操作完成 ★★★
                    if torch.cuda.is_available() and self.device == "cuda":
                        torch.cuda.synchronize()

                # 将新生成的嵌入填充回原列表
                for i, embedding in enumerate(new_embeddings_list):
                    original_index = indices_to_embed[i]
                    embeddings[original_index] = embedding
                    self._cache_embedding(texts_to_embed[i], embedding)

                if len(texts_to_embed) > 0:
                    self._save_cache()

            except Exception as e:
                logger.error(f"嵌入生成失败: {e}")
                # 异常情况下也要清理显存
                if self.auto_cleanup:
                    self.synchronize_and_cleanup()
                raise
            finally:
                # 任务完成后自动清理
                if self.auto_cleanup:
                    self.synchronize_and_cleanup()
                    logger.debug("任务完成后自动清理执行")
                    
                if self.memory_monitor:
                    # ★★★ 延迟监控确保清理生效 ★★★
                    if torch.cuda.is_available() and self.device == "cuda":
                        torch.cuda.synchronize()
                    self.log_memory_usage("嵌入完成")

        # ★★★ 优化点 2: 对整个矩阵进行 tolist() 转换 ★★★
        # 首先将列表（其中可能混合了np数组和列表）统一转换为一个大的np数组
        final_embeddings_np = np.array(embeddings, dtype=np.float32)
        return final_embeddings_np.tolist()

    def embed_query(self, text: str) -> List[float]:
        """
        嵌入查询文本

        Args:
            text: 查询文本

        Returns:
            嵌入向量
        """
        embeddings = self.embed_documents([text])
        return embeddings[0] if embeddings else []

    def get_embedding_dimension(self) -> int:
        """
        获取嵌入维度

        Returns:
            嵌入维度
        """
        return 1024  # BGE-M3固定维度

    def health_check(self) -> bool:
        """
        健康检查

        Returns:
            服务是否健康
        """
        try:
            if self.model is None:
                return False

            # 测试嵌入
            test_result = self.embed_query("health check")
            
            # 检查显存状态（如果启用监控）
            if self.memory_monitor and torch.cuda.is_available() and self.device == "cuda":
                memory_info = self.get_memory_info()
                if memory_info["memory_usage_percent"] > 95:  # 显存使用率过高
                    logger.warning("健康检查: GPU显存使用率过高")
                    return False
            
            return len(test_result) > 0
        except Exception as e:
            logger.error(f"Embedding service health check failed: {e}")
            return False

    def check_memory_leak(self) -> Dict[str, Any]:
        """
        检查潜在的显存泄漏
        
        Returns:
            泄漏检查结果
        """
        result = {
            "leak_detected": False,
            "warning_level": "normal",
            "recommendations": []
        }
        
        try:
            if not (torch.cuda.is_available() and self.device == "cuda"):
                result["recommendations"].append("CPU模式，无需检查显存泄漏")
                return result
                
            memory_info = self.get_memory_info()
            usage_percent = memory_info.get("memory_usage_percent", 0)
            allocated = memory_info.get("allocated", 0)
            reserved = memory_info.get("reserved", 0)
            
            # 检查显存使用率
            if usage_percent > 95:
                result["leak_detected"] = True
                result["warning_level"] = "critical"
                result["recommendations"].extend([
                    "显存使用率过高，疑似泄漏",
                    "建议立即执行强制清理",
                    "考虑重启服务"
                ])
            elif usage_percent > self.cleanup_threshold:
                result["warning_level"] = "warning"
                result["recommendations"].extend([
                    "显存使用率较高，需要关注",
                    "建议执行清理操作"
                ])
            
            # 检查分配与保留的比率
            if allocated > 0 and reserved > 0:
                fragmentation_ratio = (reserved - allocated) / reserved
                if fragmentation_ratio > 0.5:
                    result["leak_detected"] = True
                    result["recommendations"].append("显存碎片化严重，建议重载模型")
            
            # 检查最大分配内存
            max_allocated = memory_info.get("max_allocated", 0)
            if max_allocated > allocated * 2:
                result["recommendations"].append("历史峰值显存较高，注意监控")
            
            result.update({
                "current_usage_percent": usage_percent,
                "allocated_gb": allocated,
                "reserved_gb": reserved,
                "fragmentation_ratio": fragmentation_ratio if allocated > 0 and reserved > 0 else 0
            })
            
        except Exception as e:
            result["error"] = str(e)
            result["recommendations"].append("显存检查失败，请手动检查")
        
        return result
