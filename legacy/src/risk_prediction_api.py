"""
风险预测主接口
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from config import settings
    from src.file_coded_vector_store import FileCodedVectorStore
    from src.risk_analyzer import RiskAnalyzer
    from src.excel_risk_processor import ExcelRiskProcessor
    from src.csv_risk_processor import CSVRiskProcessor
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.error(f"导入配置失败: {e}")

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RiskPredictionAPI:
    """风险预测主接口"""

    def __init__(
        self,
        api_key: Optional[str] = None,
        model_name: str = None,
        base_persist_dir: str = "./cache/file_coded_chroma",
    ):
        """
        初始化风险预测API

        Args:
            api_key: DeepSeek API 密钥
            model_name: 模型名称
            base_persist_dir: 向量存储基础目录
        """
        # 初始化组件
        self.vector_store_manager = FileCodedVectorStore(
            base_persist_dir=base_persist_dir
        )
        self.risk_analyzer = RiskAnalyzer(
            file_coded_vector_store=self.vector_store_manager,
            api_key=api_key,
            model_name=model_name,
        )
        self.excel_processor = ExcelRiskProcessor()
        self.csv_processor = CSVRiskProcessor()

        logger.info("✅ 风险预测API初始化完成")

    def upload_excel_and_get_code(self, file_path: str) -> Dict[str, Any]:
        """
        上传Excel文件并获取文件编码

        Args:
            file_path: Excel文件路径

        Returns:
            包含文件编码和处理结果的字典
        """
        try:
            logger.info(f"开始处理Excel文件: {file_path}")

            # 处理Excel文件
            file_code, documents, file_info = self.excel_processor.process_excel_file(
                file_path
            )

            if not file_code or not documents:
                return {
                    "success": False,
                    "error": "Excel文件处理失败",
                    "file_path": file_path,
                }

            # 创建向量存储
            self.vector_store_manager.create_vector_store_for_file(
                file_code=file_code, documents=documents, file_info=file_info
            )

            result = {
                "success": True,
                "file_code": file_code,
                "file_path": file_path,
                "document_count": len(documents),
                "file_info": file_info,
                "upload_timestamp": datetime.now().isoformat(),
            }

            logger.info(f"✅ Excel文件处理完成，文件编码: {file_code}")
            return result

        except Exception as e:
            logger.error(f"上传Excel文件失败: {e}")
            return {"success": False, "error": str(e), "file_path": file_path}

    def upload_csv_and_get_code(self, file_path: str) -> Dict[str, Any]:
        """
        上传CSV文件并获取文件编码

        Args:
            file_path: CSV文件路径

        Returns:
            包含文件编码和处理结果的字典
        """
        try:
            logger.info(f"开始处理CSV文件: {file_path}")

            # 处理CSV文件
            file_code, documents, file_info = self.csv_processor.process_csv_file(
                file_path
            )

            if not file_code or not documents:
                return {
                    "success": False,
                    "error": "CSV文件处理失败",
                    "file_path": file_path,
                }

            # 创建向量存储
            self.vector_store_manager.create_vector_store_for_file(
                file_code=file_code, documents=documents, file_info=file_info
            )

            result = {
                "success": True,
                "file_code": file_code,
                "file_path": file_path,
                "document_count": len(documents),
                "file_info": file_info,
                "upload_timestamp": datetime.now().isoformat(),
            }

            logger.info(f"✅ CSV文件处理完成，文件编码: {file_code}")
            return result

        except Exception as e:
            logger.error(f"上传CSV文件失败: {e}")
            return {"success": False, "error": str(e), "file_path": file_path}

    def predict_by_file_code(self, file_code: str) -> Dict[str, Any]:
        """
        根据文件编码进行风险预测

        Args:
            file_code: 文件编码

        Returns:
            完整的风险预测报告
        """
        try:
            logger.info(f"开始为文件编码 {file_code} 进行风险预测")

            # 检查文件编码是否存在
            if file_code not in self.vector_store_manager.list_file_codes():
                return {
                    "success": False,
                    "error": f"文件编码 {file_code} 不存在",
                    "file_code": file_code,
                }

            # 生成风险报告
            risk_report = self.risk_analyzer.generate_risk_report(file_code)

            if risk_report.get("success", False):
                logger.info(f"✅ 风险预测完成，文件编码: {file_code}")
            else:
                logger.error(f"风险预测失败，文件编码: {file_code}")

            return risk_report

        except Exception as e:
            logger.error(f"风险预测失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_code": file_code,
                "prediction_timestamp": datetime.now().isoformat(),
            }

    def get_risk_summary(self, file_code: str) -> str:
        """
        获取风险分析摘要

        Args:
            file_code: 文件编码

        Returns:
            风险分析摘要文本
        """
        return self.risk_analyzer.get_risk_summary(file_code)

    def list_available_files(self) -> List[Dict[str, Any]]:
        """
        列出所有可用的文件

        Returns:
            文件信息列表
        """
        file_codes = self.vector_store_manager.list_file_codes()
        file_list = []

        for file_code in file_codes:
            file_info = self.vector_store_manager.get_file_info_by_code(file_code)
            if file_info:
                file_list.append({"file_code": file_code, "file_info": file_info})
            else:
                file_list.append({"file_code": file_code, "file_info": {}})

        return file_list

    def remove_file(self, file_code: str) -> Dict[str, Any]:
        """
        删除文件及其向量存储

        Args:
            file_code: 文件编码

        Returns:
            删除结果
        """
        try:
            success = self.vector_store_manager.remove_file_code(file_code)

            if success:
                logger.info(f"✅ 成功删除文件编码: {file_code}")
                return {
                    "success": True,
                    "file_code": file_code,
                    "message": "文件删除成功",
                }
            else:
                return {
                    "success": False,
                    "file_code": file_code,
                    "error": "文件删除失败",
                }

        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return {"success": False, "file_code": file_code, "error": str(e)}

    def get_system_stats(self) -> Dict[str, Any]:
        """
        获取系统统计信息

        Returns:
            系统统计信息
        """
        vector_stats = self.vector_store_manager.get_statistics()
        analyzer_info = self.risk_analyzer.get_analyzer_info()

        return {
            "vector_store_stats": vector_stats,
            "analyzer_info": analyzer_info,
            "api_info": {
                "initialized_at": datetime.now().isoformat(),
                "components": [
                    "FileCodedVectorStore",
                    "RiskAnalyzer",
                    "ExcelRiskProcessor",
                    "CSVRiskProcessor",
                ],
            },
        }


# 便捷函数
def create_risk_prediction_api(
    api_key: Optional[str] = None,
    model_name: str = None,
    base_persist_dir: str = "./cache/file_coded_chroma",
) -> RiskPredictionAPI:
    """
    创建风险预测API实例

    Args:
        api_key: DeepSeek API 密钥
        model_name: 模型名称
        base_persist_dir: 向量存储基础目录

    Returns:
        风险预测API实例
    """
    return RiskPredictionAPI(
        api_key=api_key, model_name=model_name, base_persist_dir=base_persist_dir
    )


# 主要使用示例
def main_usage_example():
    """主要使用示例"""
    # 创建API实例
    api = create_risk_prediction_api()

    # 上传CSV文件（推荐方式）
    upload_result = api.upload_csv_and_get_code("path/to/user_data.csv")
    if upload_result["success"]:
        file_code = upload_result["file_code"]
        print(f"CSV文件上传成功，文件编码: {file_code}")

        # 进行风险预测
        risk_report = api.predict_by_file_code(file_code)
        if risk_report["success"]:
            print(f"违约概率: {risk_report['default_probability']:.2%}")
            print(f"风险等级: {risk_report['risk_level']}")
            print(f"分析摘要: {risk_report['analysis_summary'][:200]}...")
        else:
            print(f"风险预测失败: {risk_report['error']}")
    else:
        print(f"CSV文件上传失败: {upload_result['error']}")

    # 也支持Excel文件（兼容性）
    # upload_result = api.upload_excel_and_get_code("path/to/user_data.xlsx")


if __name__ == "__main__":
    main_usage_example()
