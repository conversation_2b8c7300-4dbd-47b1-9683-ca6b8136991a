#!/bin/bash

# Deep Risk RAG 异步架构服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 显示帮助信息
show_help() {
    echo "Deep Risk RAG 异步架构服务启动脚本"
    echo ""
    echo "用法: $0 [选项] [服务]"
    echo ""
    echo "服务:"
    echo "  all              启动所有服务 [默认]"
    echo "  redis            启动Redis服务"
    echo "  chromadb         启动ChromaDB服务"
    echo "  unified          启动统一Web服务"
    echo "  worker           启动Celery Worker服务"
    echo "  flower           启动Flower监控"
    echo ""
    echo "选项:"
    echo "  -d, --dev        开发模式"
    echo "  -p, --prod       生产模式"
    echo "  -b, --background 后台运行"
    echo "  -s, --stop       停止服务"
    echo "  -r, --restart    重启服务"
    echo "  -l, --logs       显示日志"
    echo "  -h, --help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 启动所有服务（开发模式）"
    echo "  $0 -p all             # 启动所有服务（生产模式）"
    echo "  $0 -d unified         # 启动统一服务（开发模式）"
    echo "  $0 -s worker          # 停止Worker服务"
    echo "  $0 -l                 # 查看所有服务日志"
}

# 检查依赖
check_dependencies() {
    print_header "🔍 检查依赖..."
    
    # 检查Python
    if ! command -v python &> /dev/null; then
        print_error "Python未找到，请先安装Python"
        exit 1
    fi
    
    # 检查pip
    if ! command -v pip &> /dev/null; then
        print_error "pip未找到，请先安装pip"
        exit 1
    fi
    
    # 检查Redis（如果需要本地启动）
    if [ "$SERVICE" = "redis" ] || [ "$SERVICE" = "all" ]; then
        if ! command -v redis-server &> /dev/null; then
            print_warning "redis-server未找到，将尝试使用Docker启动Redis"
        fi
    fi
    
    print_message "依赖检查完成"
}

# 检查环境变量
check_env_vars() {
    print_header "🔧 检查环境变量..."
    
    # 必需的环境变量
    local required_vars=("DEEPSEEK_API_KEY")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        print_warning "以下环境变量未设置:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        print_warning "请在.env文件中设置或使用export命令设置"
    fi
    
    # 加载.env文件
    if [ -f ".env" ]; then
        print_message "加载.env配置文件"
        export $(cat .env | grep -v '^#' | xargs)
    else
        print_warning ".env文件不存在，将使用默认配置"
    fi
    
    # 设置默认值
    export HOST=${HOST:-"0.0.0.0"}
    export REDIS_HOST=${REDIS_HOST:-"localhost"}
    export REDIS_PORT=${REDIS_PORT:-"6379"}
    export CHROMA_HOST=${CHROMA_HOST:-"localhost"}
    export CHROMA_PORT=${CHROMA_PORT:-"8001"}
    export LOG_LEVEL=${LOG_LEVEL:-"INFO"}
    
    if [ "$MODE" = "dev" ]; then
        export DEBUG=true
        export LOG_LEVEL=DEBUG
    fi
    
    print_message "环境变量配置完成"
}

# 安装依赖
install_dependencies() {
    print_header "📦 安装Python依赖..."
    
    # 检查虚拟环境
    if [ -n "$VIRTUAL_ENV" ]; then
        print_message "使用虚拟环境: $VIRTUAL_ENV"
    else
        print_warning "未检测到虚拟环境，建议使用虚拟环境"
    fi
    
    # 安装依赖
    pip install -r requirements.txt
    
    print_message "依赖安装完成"
}

# 启动Redis服务
start_redis() {
    print_header "🔴 启动Redis服务..."
    
    # 检查Redis是否已运行
    if redis-cli ping &> /dev/null; then
        print_message "Redis服务已在运行"
        return 0
    fi
    
    # 尝试启动Redis
    if command -v redis-server &> /dev/null; then
        if [ "$BACKGROUND" = "true" ]; then
            nohup redis-server --port $REDIS_PORT --daemonize yes > /dev/null 2>&1 &
        else
            redis-server --port $REDIS_PORT
        fi
    else
        print_warning "本地Redis未安装，尝试使用Docker启动..."
        docker run -d --name deep-risk-redis -p $REDIS_PORT:6379 redis:7-alpine
    fi
    
    print_message "Redis服务启动完成"
}

# 启动ChromaDB服务
start_chromadb() {
    print_header "🗄️  启动ChromaDB服务..."
    
    # 检查ChromaDB是否已运行
    if curl -f http://$CHROMA_HOST:$CHROMA_PORT/api/v1/heartbeat &> /dev/null; then
        print_message "ChromaDB服务已在运行"
        return 0
    fi
    
    if [ "$BACKGROUND" = "true" ]; then
        nohup chroma run --host $CHROMA_HOST --port $CHROMA_PORT --path $CHROMA_PERSIST_DIR > logs/chromadb.log 2>&1 &
        echo $! > pids/chromadb.pid
    else
        chroma run --host $CHROMA_HOST --port $CHROMA_PORT --path $CHROMA_PERSIST_DIR
    fi
    
    print_message "ChromaDB服务启动完成"
}

# 启动统一Web服务
start_unified() {
    print_header "🌐 启动统一Web服务..."
    
    local port=${PORT:-8000}
    
    # 检查端口是否被占用
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
        print_warning "端口 $port 已被占用"
        return 1
    fi
    
    if [ "$BACKGROUND" = "true" ]; then
        nohup python -m uvicorn services.unified_service.main:app --host $HOST --port $port > logs/unified.log 2>&1 &
        echo $! > pids/unified.pid
    else
        if [ "$MODE" = "dev" ]; then
            python -m uvicorn services.unified_service.main:app --host $HOST --port $port --reload
        else
            python -m uvicorn services.unified_service.main:app --host $HOST --port $port
        fi
    fi
    
    print_message "统一Web服务启动完成"
}

# 启动Celery Worker服务
start_worker() {
    print_header "⚙️  启动Celery Worker服务..."

    local loglevel=${LOG_LEVEL,,}

    # 获取系统兼容性配置
    local worker_config=$(python -c "
import sys
sys.path.insert(0, '.')
from shared.celery_config import get_worker_startup_args
args = get_worker_startup_args()
print(' '.join([f'--{k} {v}' for k, v in args.items() if v is not None]))
")

    print_message "系统兼容性配置: $worker_config"

    if [ "$BACKGROUND" = "true" ]; then
        nohup celery -A services.worker_service.worker worker \
            --loglevel=$loglevel \
            --prefetch-multiplier=1 \
            $worker_config > logs/worker.log 2>&1 &
        echo $! > pids/worker.pid
    else
        celery -A services.worker_service.worker worker \
            --loglevel=$loglevel \
            --prefetch-multiplier=1 \
            $worker_config
    fi

    print_message "Celery Worker服务启动完成"
}

# 启动Flower监控
start_flower() {
    print_header "🌸 启动Flower监控..."
    
    local flower_port=${FLOWER_PORT:-5555}
    
    # 检查端口是否被占用
    if lsof -Pi :$flower_port -sTCP:LISTEN -t >/dev/null; then
        print_warning "端口 $flower_port 已被占用"
        return 1
    fi
    
    if [ "$BACKGROUND" = "true" ]; then
        nohup celery -A services.worker_service.worker flower \
            --port=$flower_port > logs/flower.log 2>&1 &
        echo $! > pids/flower.pid
    else
        celery -A services.worker_service.worker flower --port=$flower_port
    fi
    
    print_message "Flower监控启动完成"
}

# 停止服务
stop_service() {
    local service_name=$1
    print_header "🛑 停止${service_name}服务..."
    
    local pid_file="pids/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            rm "$pid_file"
            print_message "${service_name}服务已停止"
        else
            print_warning "${service_name}服务进程不存在"
            rm "$pid_file"
        fi
    else
        print_warning "${service_name}服务PID文件不存在"
    fi
}

# 显示服务状态
show_status() {
    print_header "📊 服务状态:"
    echo ""
    
    # 检查各服务状态
    local services=("redis" "chromadb" "unified" "worker" "flower")
    
    for service in "${services[@]}"; do
        local pid_file="pids/${service}.pid"
        local status="❌ 未运行"
        
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                status="✅ 运行中 (PID: $pid)"
            else
                status="❌ 进程不存在"
                rm "$pid_file"
            fi
        fi
        
        printf "%-15s %s\n" "$service:" "$status"
    done
    
    echo ""
    print_header "🌐 服务访问地址:"
    echo "  统一Web服务:  http://localhost:${PORT:-8000}"
    echo "  API文档:      http://localhost:${PORT:-8000}/docs"
    echo "  ChromaDB:     http://localhost:${CHROMA_PORT:-8001}"
    echo "  Flower监控:   http://localhost:${FLOWER_PORT:-5555}"
    echo ""
}

# 显示日志
show_logs() {
    print_header "📋 显示服务日志..."
    
    if [ "$SERVICE" = "all" ]; then
        tail -f logs/*.log 2>/dev/null || print_warning "没有找到日志文件"
    else
        tail -f "logs/${SERVICE}.log" 2>/dev/null || print_warning "没有找到${SERVICE}的日志文件"
    fi
}

# 创建必要的目录
create_directories() {
    mkdir -p logs pids data/uploads data/chromadb
}

# 默认参数
MODE="dev"
SERVICE="all"
BACKGROUND="false"
STOP="false"
RESTART="false"
LOGS="false"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--dev)
            MODE="dev"
            shift
            ;;
        -p|--prod)
            MODE="prod"
            shift
            ;;
        -b|--background)
            BACKGROUND="true"
            shift
            ;;
        -s|--stop)
            STOP="true"
            shift
            ;;
        -r|--restart)
            RESTART="true"
            shift
            ;;
        -l|--logs)
            LOGS="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        all|redis|chromadb|unified|worker|flower)
            SERVICE="$1"
            shift
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主逻辑
main() {
    print_header "🎯 Deep Risk RAG 异步架构服务管理"
    echo "模式: $MODE | 服务: $SERVICE"
    echo ""
    
    # 创建必要目录
    create_directories
    
    # 如果是显示日志
    if [ "$LOGS" = "true" ]; then
        show_logs
        exit 0
    fi
    
    # 如果是停止服务
    if [ "$STOP" = "true" ]; then
        if [ "$SERVICE" = "all" ]; then
            for service in flower worker unified chromadb redis; do
                stop_service "$service"
            done
        else
            stop_service "$SERVICE"
        fi
        exit 0
    fi
    
    # 检查依赖和环境
    check_dependencies
    check_env_vars
    
    # 如果是重启服务
    if [ "$RESTART" = "true" ]; then
        if [ "$SERVICE" = "all" ]; then
            for service in flower worker unified chromadb redis; do
                stop_service "$service"
            done
        else
            stop_service "$SERVICE"
        fi
        sleep 2
    fi
    
    # 安装依赖（开发模式）
    if [ "$MODE" = "dev" ]; then
        install_dependencies
    fi
    
    # 启动服务
    case $SERVICE in
        all)
            start_redis
            sleep 2
            start_chromadb
            sleep 3
            start_unified
            sleep 2
            start_worker
            sleep 2
            start_flower
            ;;
        redis)
            start_redis
            ;;
        chromadb)
            start_chromadb
            ;;
        unified)
            start_unified
            ;;
        worker)
            start_worker
            ;;
        flower)
            start_flower
            ;;
    esac
    
    # 显示状态
    if [ "$BACKGROUND" = "true" ]; then
        sleep 3
        show_status
    fi
}

# 执行主函数
main
