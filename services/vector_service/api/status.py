"""
状态查询API
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import traceback

from shared.utils.logger import get_logger
from services.vector_service.models.responses import StatusResponse
from services.vector_service.core.vector_manager import VectorManager
from services.vector_service.core.embeddings import BGEEmbeddingService

logger = get_logger(__name__)

router = APIRouter(prefix="/status", tags=["状态查询"])


def get_vector_manager() -> VectorManager:
    """获取向量管理器依赖"""
    from services.vector_service.main import get_vector_manager
    return get_vector_manager()


def get_embedding_service() -> BGEEmbeddingService:
    """获取嵌入服务依赖"""
    from services.vector_service.main import get_embedding_service
    return get_embedding_service()


@router.get("/health")
async def health_status(
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """
    获取系统健康状态
    """
    try:
        health_check = vector_manager.health_check()
        
        # 计算整体健康状态
        overall_healthy = all(health_check.values())
        
        return {
            "status": "healthy" if overall_healthy else "degraded",
            "components": health_check,
            "timestamp": "2024-01-01T00:00:00Z"  # 可以使用实际时间戳
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@router.get("/memory")
async def memory_status(
    embedding_service: BGEEmbeddingService = Depends(get_embedding_service)
) -> Dict[str, Any]:
    """
    获取GPU显存使用状态
    """
    try:
        if hasattr(embedding_service, 'get_memory_info'):
            memory_info = embedding_service.get_memory_info()
            
            # 添加状态评估
            memory_status = "healthy"
            if memory_info.get("cuda_available", False):
                usage_percent = memory_info.get("memory_usage_percent", 0)
                if usage_percent > 90:
                    memory_status = "critical"
                elif usage_percent > 75:
                    memory_status = "warning"
            
            return {
                "status": memory_status,
                "memory_info": memory_info,
                "recommendations": _get_memory_recommendations(memory_info)
            }
        else:
            return {
                "status": "unavailable",
                "message": "Memory monitoring not supported",
                "memory_info": {},
                "recommendations": []
            }
            
    except Exception as e:
        logger.error(f"Memory status check failed: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Memory status check failed: {str(e)}"
        )


@router.post("/memory/cleanup")
async def cleanup_memory(
    force: bool = False,
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """
    手动清理GPU显存
    
    Args:
        force: 是否强制清理（包括模型重载）
    """
    try:
        result = vector_manager.manual_cleanup_memory(force=force)
        
        if result["success"]:
            return {
                "status": "success",
                "message": result["message"],
                "memory_before": result.get("memory_before"),
                "memory_after": result.get("memory_after"),
                "force_cleanup": force
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=result["message"]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Memory cleanup failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"Memory cleanup failed: {str(e)}"
        )


@router.get("/files")
async def list_files_status(
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """
    获取所有文件的状态
    """
    try:
        files = vector_manager.list_files()
        
        return {
            "total_files": len(files),
            "files": [
                {
                    "file_code": file_info.file_code,
                    "filename": file_info.filename,
                    "status": file_info.status.value,
                    "document_count": file_info.document_count,
                    "uploaded_at": file_info.uploaded_at.isoformat() if file_info.uploaded_at else None,
                    "updated_at": file_info.updated_at.isoformat() if file_info.updated_at else None,
                }
                for file_info in files
            ]
        }
    except Exception as e:
        logger.error(f"List files failed: {e}")
        raise HTTPException(status_code=500, detail=f"List files failed: {str(e)}")


@router.get("/files/{file_code}")
async def get_file_status(
    file_code: str,
    vector_manager: VectorManager = Depends(get_vector_manager)
) -> Dict[str, Any]:
    """
    获取特定文件的状态
    """
    try:
        file_info = vector_manager.get_file_status(file_code)
        
        if not file_info:
            raise HTTPException(status_code=404, detail=f"File {file_code} not found")
        
        return {
            "file_code": file_info.file_code,
            "filename": file_info.filename,
            "status": file_info.status.value,
            "document_count": file_info.document_count,
            "uploaded_at": file_info.uploaded_at.isoformat() if file_info.uploaded_at else None,
            "updated_at": file_info.updated_at.isoformat() if file_info.updated_at else None,
            "file_size": file_info.file_size,
            "file_type": file_info.file_type,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get file status failed: {e}")
        raise HTTPException(status_code=500, detail=f"Get file status failed: {str(e)}")


@router.get("/memory/leak-check")
async def check_memory_leak(
    embedding_service: BGEEmbeddingService = Depends(get_embedding_service)
) -> Dict[str, Any]:
    """
    检查潜在的显存泄漏
    """
    try:
        if hasattr(embedding_service, 'check_memory_leak'):
            leak_check_result = embedding_service.check_memory_leak()
            
            return {
                "status": "success",
                "leak_check": leak_check_result,
                "timestamp": "2024-01-01T00:00:00Z"  # 可以使用实际时间戳
            }
        else:
            return {
                "status": "unavailable",
                "message": "Memory leak detection not supported",
                "leak_check": {}
            }
            
    except Exception as e:
        logger.error(f"Memory leak check failed: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Memory leak check failed: {str(e)}"
        )


def _get_memory_recommendations(memory_info: Dict[str, Any]) -> list:
    """
    根据显存使用情况提供建议
    """
    recommendations = []
    
    if not memory_info.get("cuda_available", False):
        # CPU模式的建议
        if memory_info.get("mode") == "CPU":
            ram_percent = memory_info.get("system_ram_percent", 0)
            if ram_percent > 90:
                recommendations.extend([
                    "系统内存使用率过高，建议清理内存",
                    "考虑重启服务或减小批处理大小"
                ])
            elif ram_percent > 75:
                recommendations.extend([
                    "系统内存使用率较高，建议定期清理",
                    "监控后续任务的内存使用"
                ])
            else:
                recommendations.append("CPU模式运行正常，内存使用合理")
        else:
            recommendations.append("使用CPU模式，无需GPU显存管理")
        return recommendations
    
    # GPU模式的建议
    usage_percent = memory_info.get("memory_usage_percent", 0)
    
    if usage_percent > 90:
        recommendations.extend([
            "显存使用率过高，建议立即清理",
            "考虑使用force=true进行强制清理",
            "检查是否有显存泄漏"
        ])
    elif usage_percent > 75:
        recommendations.extend([
            "显存使用率较高，建议定期清理",
            "监控后续向量化任务的显存使用"
        ])
    elif usage_percent < 10:
        recommendations.append("显存使用率正常，系统运行良好")
    
    return recommendations
