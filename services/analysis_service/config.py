"""
分析服务配置
"""

import os
from typing import Optional
from shared.utils.config import ServiceConfig


class AnalysisServiceConfig(ServiceConfig):
    """分析服务配置"""

    # 服务信息
    service_name: str = "analysis-service"
    service_version: str = "1.0.0"
    port: int = 8000

    # 向量服务配置
    vector_service_url: str = os.getenv("VECTOR_SERVICE_URL", "http://localhost:8002")

    # ChromaDB配置 (直连)
    chroma_host: str = os.getenv("CHROMA_HOST", "localhost")
    chroma_port: int = int(os.getenv("CHROMA_PORT", "8001"))
    chroma_api_version: str = os.getenv("CHROMA_API_VERSION", "v1")  # 默认v1保持兼容性

    # LLM配置
    llm_provider: str = os.getenv("LLM_PROVIDER", "deepseek")
    deepseek_api_key: str = os.getenv("DEEPSEEK_API_KEY", "")
    deepseek_model: str = os.getenv("DEEPSEEK_MODEL", "deepseek-reasoner")
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_model: str = os.getenv("OPENAI_MODEL", "gpt-4")

    # 分析配置
    temperature: float = float(os.getenv("TEMPERATURE", "0.1"))
    max_tokens: int = int(os.getenv("MAX_TOKENS", "2000"))
    enable_streaming: bool = (
        os.getenv("ENABLE_STREAMING_OUTPUT", "false").lower() == "true"
    )

    # 检索配置
    retrieval_top_k: int = int(os.getenv("RETRIEVAL_TOP_K", "5"))
    similarity_threshold: float = float(os.getenv("SIMILARITY_THRESHOLD", "0.7"))

    # 提示词配置
    prompts_dir: str = os.getenv("PROMPTS_DIR", "./prompts")
    risk_analysis_prompt_file: str = os.getenv(
        "RISK_ANALYSIS_PROMPT_FILE", "risk_analysis/default_prompt.md"
    )

    # 结果存储配置
    results_dir: str = os.getenv("RESULTS_DIR", "./cache/analysis_results")
    enable_result_cache: bool = True

    @property
    def chroma_url(self) -> str:
        """ChromaDB服务URL"""
        return f"http://{self.chroma_host}:{self.chroma_port}"

    def build_chroma_api_path(self, endpoint: str) -> str:
        """构建ChromaDB API路径"""
        return f"/api/{self.chroma_api_version}/{endpoint.lstrip('/')}"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
config = AnalysisServiceConfig()
