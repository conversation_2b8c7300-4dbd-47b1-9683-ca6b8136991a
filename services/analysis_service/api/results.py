"""
分析结果API路由
"""

from typing import Optional, List
from fastapi import APIRouter, HTTPException, Query

from shared.utils.logger import get_logger
from services.analysis_service.models.responses import AnalysisResultResponse
from services.analysis_service.core.risk_analyzer import RiskAnalyzer
from shared.models.analysis_result import AnalysisResult, AnalysisStatus

logger = get_logger(__name__)

router = APIRouter(prefix="/result", tags=["results"])

# 全局实例
risk_analyzer = RiskAnalyzer()


@router.get("/{analysis_id}", response_model=AnalysisResultResponse)
async def get_analysis_result(analysis_id: str):
    """
    获取分析结果

    Args:
        analysis_id: 分析ID

    Returns:
        分析结果
    """
    try:
        result = risk_analyzer.get_analysis_result(analysis_id)

        if not result:
            raise HTTPException(
                status_code=404, detail=f"Analysis result {analysis_id} not found"
            )

        return AnalysisResultResponse(
            success=True,
            message="Analysis result retrieved successfully",
            analysis_result=result,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分析结果失败: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get analysis result: {str(e)}"
        )


@router.get("/")
async def list_analysis_results(
    file_code: Optional[str] = Query(None, description="按文件编码过滤"),
    status: Optional[str] = Query(None, description="按状态过滤"),
    limit: int = Query(20, description="返回结果数量限制"),
    offset: int = Query(0, description="结果偏移量"),
):
    """
    列出分析结果

    Args:
        file_code: 文件编码过滤
        status: 状态过滤
        limit: 结果数量限制
        offset: 结果偏移量

    Returns:
        分析结果列表
    """
    try:
        # 简化实现：从结果目录读取所有结果文件
        import json
        from pathlib import Path

        results = []
        results_dir = risk_analyzer.results_dir

        if not results_dir.exists():
            return {
                "success": True,
                "results": [],
                "total": 0,
                "limit": limit,
                "offset": offset,
            }

        # 读取所有结果文件
        for result_file in results_dir.glob("*.json"):
            try:
                with open(result_file, "r", encoding="utf-8") as f:
                    data = json.load(f)

                result = AnalysisResult(**data)

                # 应用过滤条件
                if file_code and result.file_code != file_code:
                    continue

                if status and result.status != status:
                    continue

                results.append(result)

            except Exception as e:
                logger.warning(f"跳过无效结果文件 {result_file}: {e}")
                continue

        # 按创建时间排序
        results.sort(key=lambda x: x.created_at, reverse=True)

        # 应用分页
        total = len(results)
        paginated_results = results[offset : offset + limit]

        return {
            "success": True,
            "results": [result.dict() for result in paginated_results],
            "total": total,
            "limit": limit,
            "offset": offset,
        }

    except Exception as e:
        logger.error(f"列出分析结果失败: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to list analysis results: {str(e)}"
        )


@router.delete("/{analysis_id}")
async def delete_analysis_result(analysis_id: str):
    """
    删除分析结果

    Args:
        analysis_id: 分析ID

    Returns:
        删除结果
    """
    try:
        result_file = risk_analyzer.results_dir / f"{analysis_id}.json"

        if not result_file.exists():
            raise HTTPException(
                status_code=404, detail=f"Analysis result {analysis_id} not found"
            )

        result_file.unlink()

        return {
            "success": True,
            "message": f"Analysis result {analysis_id} deleted successfully",
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除分析结果失败: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete analysis result: {str(e)}"
        )


@router.get("/{analysis_id}/summary")
async def get_analysis_summary(analysis_id: str):
    """
    获取分析摘要（简化版本）

    Args:
        analysis_id: 分析ID

    Returns:
        分析摘要
    """
    try:
        result = risk_analyzer.get_analysis_result(analysis_id)

        if not result:
            raise HTTPException(
                status_code=404, detail=f"Analysis result {analysis_id} not found"
            )

        return {
            "success": True,
            "analysis_id": result.analysis_id,
            "file_code": result.file_code,
            "status": result.status,
            "default_probability": result.default_probability,
            "risk_level": result.risk_level,
            "risk_score": result.risk_score,
            "key_factors": result.key_factors,
            "recommendations": result.recommendations,
            "confidence_score": result.confidence_score,
            "processing_time": result.processing_time,
            "created_at": result.created_at,
            "completed_at": result.completed_at,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分析摘要失败: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get analysis summary: {str(e)}"
        )
