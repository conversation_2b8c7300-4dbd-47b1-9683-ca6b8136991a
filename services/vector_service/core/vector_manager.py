"""
向量管理器
负责向量存储和检索的管理 - 本地ChromaDB模式
"""

import gc
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from langchain_core.documents import Document
from langchain_chroma import Chroma
from shared.utils.logger import get_logger
from shared.models.file_info import FileInfo, FileStatus
from services.vector_service.config import config
from services.vector_service.core.embeddings import BGEEmbeddingService
from shared.utils.windows_file_utils import (
    force_garbage_collection, 
    safe_remove_directory, 
    safe_remove_file,
    is_windows
)

logger = logging.getLogger(__name__)


class VectorManager:
    """向量管理器 - 本地ChromaDB模式"""

    def __init__(
        self,
        base_persist_dir: str = None,
        embedding_service: BGEEmbeddingService = None,
    ):
        """
        初始化向量管理器

        Args:
            base_persist_dir: 基础持久化目录
            embedding_service: 嵌入服务
        """
        # 基础持久化目录
        self.base_persist_dir = Path(base_persist_dir or config.chroma_persist_dir)
        self.base_persist_dir.mkdir(parents=True, exist_ok=True)

        # 初始化嵌入服务
        self.embedding_service = embedding_service or BGEEmbeddingService()

        # 存储已创建的向量存储实例
        self._vector_stores: Dict[str, Chroma] = {}

        # 文件信息存储
        self.metadata_dir = Path(config.cache_dir) / "file_metadata"
        self.metadata_dir.mkdir(parents=True, exist_ok=True)

        logger.info(
            f"Vector manager initialized with local ChromaDB: {self.base_persist_dir}"
        )

    def get_collection_name_by_code(self, file_code: str) -> str:
        """根据文件编码获取集合名称"""
        return f"risk_analysis_{file_code}"

    def get_persist_dir_by_code(self, file_code: str) -> str:
        """根据文件编码获取持久化目录"""
        return str(self.base_persist_dir / file_code)

    def get_vector_store_by_code(self, file_code: str) -> Optional[Chroma]:
        """
        根据文件编码获取向量存储

        Args:
            file_code: 文件编码

        Returns:
            向量存储实例，如果不存在则返回None
        """
        # 先检查缓存
        if file_code in self._vector_stores:
            return self._vector_stores[file_code]

        # 检查持久化目录是否存在
        persist_dir = self.get_persist_dir_by_code(file_code)
        if not Path(persist_dir).exists():
            logger.warning(f"文件编码 {file_code} 对应的向量存储不存在")
            return None

        # 重新加载向量存储
        collection_name = self.get_collection_name_by_code(file_code)
        vector_store = Chroma(
            collection_name=collection_name,
            embedding_function=self.embedding_service,
            persist_directory=persist_dir,
        )

        # 缓存实例
        self._vector_stores[file_code] = vector_store

        logger.info(f"重新加载文件编码 {file_code} 的向量存储")
        return vector_store

    def create_vector_store_for_file(self, file_code: str) -> Chroma:
        """
        为特定文件创建向量存储

        Args:
            file_code: 文件编码

        Returns:
            向量存储实例
        """
        # 获取集合名称和持久化目录
        collection_name = self.get_collection_name_by_code(file_code)
        persist_dir = self.get_persist_dir_by_code(file_code)

        # 确保持久化目录存在
        Path(persist_dir).mkdir(parents=True, exist_ok=True)

        # 创建向量存储实例
        vector_store = Chroma(
            collection_name=collection_name,
            embedding_function=self.embedding_service,
            persist_directory=persist_dir,
        )

        # 缓存实例
        self._vector_stores[file_code] = vector_store

        logger.info(f"为文件编码 {file_code} 创建向量存储")
        return vector_store

    def store_documents(
        self, file_code: str, documents: List[Document], file_info: FileInfo
    ) -> bool:
        """
        存储文档向量

        Args:
            file_code: 文件编码
            documents: 文档列表
            file_info: 文件信息

        Returns:
            是否存储成功
        """
        try:
            logger.info(f"Storing {len(documents)} documents for {file_code}")

            # 更新文件状态
            file_info.status = FileStatus.VECTORIZING
            file_info.updated_at = datetime.now()
            self._save_file_info(file_info)

            # 创建或获取向量存储
            vector_store = self.create_vector_store_for_file(file_code)

            # 生成文档ID
            doc_ids = [f"{file_code}_{i}" for i in range(len(documents))]

            # 添加到向量存储
            logger.info("Adding documents to vector store...")
            result_ids = vector_store.add_documents(documents=documents, ids=doc_ids)

            if result_ids:
                # 更新文件状态
                file_info.status = FileStatus.COMPLETED
                file_info.document_count = len(documents)
                file_info.updated_at = datetime.now()
                self._save_file_info(file_info)

                logger.info(
                    f"Successfully stored {len(documents)} documents for {file_code}"
                )
                
                # 向量化完成后自动清理GPU显存
                self._cleanup_after_vectorization(file_code)
                
                return True
            else:
                # 存储失败
                file_info.status = FileStatus.FAILED
                file_info.updated_at = datetime.now()
                self._save_file_info(file_info)
                
                # 失败时也要清理显存
                self._cleanup_after_vectorization(file_code, force=True)
                return False

        except Exception as e:
            logger.error(f"Failed to store documents for {file_code}: {e}")

            # 更新失败状态
            file_info.status = FileStatus.FAILED
            file_info.updated_at = datetime.now()
            self._save_file_info(file_info)
            
            # 异常时也要清理显存
            self._cleanup_after_vectorization(file_code, force=True)
            return False

    def _cleanup_after_vectorization(self, file_code: str, force: bool = False):
        """
        向量化完成后清理GPU显存
        
        Args:
            file_code: 文件编码
            force: 是否强制清理
        """
        try:
            logger.info(f"🔄 清理文件 {file_code} 向量化后的GPU显存...")
            
            # 清理嵌入服务的显存
            if hasattr(self.embedding_service, 'cleanup_memory'):
                self.embedding_service.cleanup_memory(force=force)
            
            # 记录清理完成
            logger.info(f"✅ 文件 {file_code} 的GPU显存清理完成")
            
        except Exception as e:
            logger.warning(f"⚠️  文件 {file_code} 显存清理时出现警告: {e}")

    def manual_cleanup_memory(self, force: bool = False) -> Dict[str, Any]:
        """
        手动清理GPU显存
        
        Args:
            force: 是否强制清理（包括模型）
            
        Returns:
            清理结果
        """
        result = {
            "success": False,
            "message": "",
            "memory_before": None,
            "memory_after": None
        }
        
        try:
            # 获取清理前的显存信息
            if hasattr(self.embedding_service, 'get_memory_info'):
                result["memory_before"] = self.embedding_service.get_memory_info()
            
            # 执行清理
            if hasattr(self.embedding_service, 'cleanup_memory'):
                self.embedding_service.cleanup_memory(force=force)
                
                # 获取清理后的显存信息
                if hasattr(self.embedding_service, 'get_memory_info'):
                    result["memory_after"] = self.embedding_service.get_memory_info()
                
                result["success"] = True
                result["message"] = f"GPU显存清理完成 (强制清理: {force})"
                logger.info(f"✅ {result['message']}")
            else:
                result["message"] = "嵌入服务不支持显存清理"
                logger.warning(result["message"])
                
        except Exception as e:
            result["message"] = f"显存清理失败: {e}"
            logger.error(result["message"])
        
        return result

    def get_file_status(self, file_code: str) -> Optional[FileInfo]:
        """
        获取文件状态

        Args:
            file_code: 文件编码

        Returns:
            文件信息
        """
        return self._load_file_info(file_code)

    def list_files(self) -> List[FileInfo]:
        """
        列出所有文件

        Returns:
            文件信息列表
        """
        files = []

        try:
            for metadata_file in self.metadata_dir.glob("*.json"):
                file_code = metadata_file.stem
                file_info = self._load_file_info(file_code)
                if file_info:
                    files.append(file_info)
        except Exception as e:
            logger.error(f"Failed to list files: {e}")

        return files

    def delete_file(self, file_code: str) -> bool:
        """
        删除文件和相关数据

        Args:
            file_code: 文件编码

        Returns:
            是否删除成功
        """
        try:
            # 显式关闭和清理ChromaDB连接
            if file_code in self._vector_stores:
                vector_store = self._vector_stores[file_code]
                
                # 优化：使用上下文管理器的清理逻辑
                from services.vector_service.core.chroma_context import ChromaVectorStoreContext
                context = ChromaVectorStoreContext("", None, "", False)
                context.vector_store = vector_store
                context._close_chroma_client()
                
                # 从缓存中移除
                del self._vector_stores[file_code]
                logger.debug(f"Removed vector store from cache: {file_code}")

            # 强制垃圾回收以释放文件句柄
            force_garbage_collection()

            # 删除持久化目录（使用Windows兼容的方法）
            persist_dir = Path(self.get_persist_dir_by_code(file_code))
            chroma_success = True
            if persist_dir.exists():
                chroma_success = safe_remove_directory(persist_dir, max_retries=5, retry_delay=0.5)
                if chroma_success:
                    logger.info(f"Deleted persist directory: {persist_dir}")
                else:
                    logger.error(f"Failed to delete persist directory: {persist_dir}")

            # 删除元数据文件（使用Windows兼容的方法）
            metadata_file = self.metadata_dir / f"{file_code}.json"
            metadata_success = True
            if metadata_file.exists():
                metadata_success = safe_remove_file(metadata_file, max_retries=3, retry_delay=0.3)
                if metadata_success:
                    logger.info(f"Deleted metadata file: {metadata_file}")
                else:
                    logger.error(f"Failed to delete metadata file: {metadata_file}")

            success = chroma_success and metadata_success
            if success:
                logger.info(f"Successfully deleted file: {file_code}")
            else:
                logger.warning(f"Partial deletion for file {file_code}: chroma={chroma_success}, metadata={metadata_success}")

            return success

        except Exception as e:
            logger.error(f"Failed to delete file {file_code}: {e}")
            return False

    def search_documents(
        self, file_code: str, query: str, top_k: int = 5
    ) -> Dict[str, Any]:
        """
        搜索文档

        Args:
            file_code: 文件编码
            query: 查询文本
            top_k: 返回结果数量

        Returns:
            搜索结果
        """
        try:
            # 获取向量存储
            vector_store = self.get_vector_store_by_code(file_code)
            if not vector_store:
                raise ValueError(f"Vector store not found for file_code: {file_code}")

            # 执行相似度搜索
            results = vector_store.similarity_search_with_score(query=query, k=top_k)

            # 转换结果格式
            documents = []
            metadatas = []
            distances = []
            ids = []

            for doc, score in results:
                documents.append(doc.page_content)
                metadatas.append(doc.metadata)
                distances.append(score)
                ids.append(doc.metadata.get("doc_id", ""))

            return {
                "documents": [documents] if documents else [],
                "metadatas": [metadatas] if metadatas else [],
                "distances": [distances] if distances else [],
                "ids": [ids] if ids else [],
                "query": query,
                "total_found": len(documents),
            }

        except Exception as e:
            logger.error(f"Failed to search documents for {file_code}: {e}")
            raise

    def get_collection_stats(self, file_code: str) -> Dict[str, Any]:
        """
        获取集合统计信息

        Args:
            file_code: 文件编码

        Returns:
            统计信息
        """
        try:
            vector_store = self.get_vector_store_by_code(file_code)
            if not vector_store:
                return {"exists": False}

            # 获取集合信息
            collection = vector_store._collection
            document_count = collection.count()

            return {
                "exists": True,
                "document_count": document_count,
                "collection_name": self.get_collection_name_by_code(file_code),
            }

        except Exception as e:
            logger.error(f"Failed to get collection stats for {file_code}: {e}")
            return {"exists": False, "error": str(e)}

    def _save_file_info(self, file_info: FileInfo) -> bool:
        """
        保存文件信息

        Args:
            file_info: 文件信息

        Returns:
            是否保存成功
        """
        try:
            metadata_file = self.metadata_dir / f"{file_info.file_code}.json"

            with open(metadata_file, "w", encoding="utf-8") as f:
                json.dump(
                    file_info.dict(), f, ensure_ascii=False, indent=2, default=str
                )

            return True

        except Exception as e:
            logger.error(f"Failed to save file info for {file_info.file_code}: {e}")
            return False

    def _load_file_info(self, file_code: str) -> Optional[FileInfo]:
        """
        加载文件信息

        Args:
            file_code: 文件编码

        Returns:
            文件信息
        """
        try:
            metadata_file = self.metadata_dir / f"{file_code}.json"

            if not metadata_file.exists():
                return None

            with open(metadata_file, "r", encoding="utf-8") as f:
                data = json.load(f)

            return FileInfo(**data)

        except Exception as e:
            logger.error(f"Failed to load file info for {file_code}: {e}")
            return None

    def health_check(self) -> Dict[str, bool]:
        """
        健康检查

        Returns:
            各组件健康状态
        """
        health_status = {
            "embedding_service": False,
            "vector_storage": False,
            "metadata_storage": False,
            "gpu_memory": True  # 默认正常，除非检测到问题
        }

        try:
            # 检查嵌入服务
            if self.embedding_service and hasattr(self.embedding_service, "health_check"):
                health_status["embedding_service"] = self.embedding_service.health_check()

            # 检查向量存储（使用上下文管理器）
            from services.vector_service.core.chroma_context import ChromaHealthChecker
            
            health_checker = ChromaHealthChecker(
                embedding_function=self.embedding_service,
                base_persist_dir=str(self.base_persist_dir)
            )
            
            vector_health_result = health_checker.check_vector_storage_health()
            health_status["vector_storage"] = vector_health_result["success"]
            
            if not vector_health_result["cleanup_success"]:
                logger.warning("健康检查: 测试文件清理未完全成功")

            # 检查元数据存储
            health_status["metadata_storage"] = self.metadata_dir.exists()
            
            # 检查GPU显存状态
            if hasattr(self.embedding_service, 'get_memory_info'):
                try:
                    memory_info = self.embedding_service.get_memory_info()
                    if (memory_info.get("cuda_available", False) and 
                        memory_info.get("memory_usage_percent", 0) > 95):
                        health_status["gpu_memory"] = False
                        logger.warning("健康检查: GPU显存使用率过高")
                except Exception as e:
                    logger.debug(f"GPU显存检查失败: {e}")

        except Exception as e:
            logger.error(f"Health check failed: {e}")

        return health_status
