"""
向量化服务配置
"""

import os
from typing import Optional
from pathlib import Path
from shared.utils.config import ServiceConfig


class VectorServiceConfig(ServiceConfig):
    """向量化服务配置"""

    # 服务信息
    service_name: str = "vector-service"
    service_version: str = "1.0.0"
    port: int = 8000

    # ChromaDB配置 (本地模式)
    chroma_persist_dir: str = os.getenv("CHROMA_PERSIST_DIR", "./cache/chroma_db")

    # BGE-M3模型配置 - 使用本地模型路径
    model_name: str = os.getenv("MODEL_NAME", str(Path(__file__).parent.parent.parent / "models" / "bge-m3-safetensors-only"))
    model_cache_dir: Optional[str] = os.getenv("MODEL_CACHE_DIR", "./models")
    device: str = os.getenv("DEVICE", "auto")  # auto, cpu, cuda
    batch_size: int = int(os.getenv("BATCH_SIZE", "32"))
    max_length: int = int(os.getenv("MAX_LENGTH", "8192"))
    normalize_embeddings: bool = True

    # 文件处理配置
    upload_dir: str = os.getenv("UPLOAD_DIR", "./data/uploads")
    max_file_size: int = int(os.getenv("MAX_FILE_SIZE", "100"))  # MB
    allowed_extensions: list = [".csv", ".xlsx", ".xls"]

    # 文档分块配置
    chunk_size: int = int(os.getenv("CHUNK_SIZE", "4000"))
    chunk_overlap: int = int(os.getenv("CHUNK_OVERLAP", "400"))

    # 缓存配置
    enable_embedding_cache: bool = True
    cache_dir: str = os.getenv("CACHE_DIR", "./cache")

    # GPU显存管理配置
    auto_cleanup: bool = os.getenv("AUTO_CLEANUP", "true").lower() == "true"  # 自动清理
    memory_monitor: bool = os.getenv("MEMORY_MONITOR", "true").lower() == "true"  # 显存监控
    cleanup_threshold: float = float(os.getenv("CLEANUP_THRESHOLD", "80.0"))  # 清理阈值(%)
    batch_cleanup_interval: int = int(os.getenv("BATCH_CLEANUP_INTERVAL", "10"))  # 批次清理间隔

    # 禁用PostHog遥测数据发送，避免SSL连接错误
    def __init__(self, **data):
        super().__init__(**data)
        self._disable_posthog()

    def _disable_posthog(self):
        """完全禁用所有遥测数据发送，保护隐私和数据安全"""
        # 禁用PostHog
        os.environ["POSTHOG_HOST"] = ""
        os.environ["POSTHOG_PROJECT_ID"] = ""
        os.environ["POSTHOG_API_KEY"] = ""
        os.environ["POSTHOG_FEATURE_FLAGS"] = "false"
        
        # 禁用LangChain遥测
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        os.environ["LANGCHAIN_ANALYTICS"] = "false"
        os.environ["LANGCHAIN_TRACING"] = "false"
        os.environ["LANGCHAIN_TRACKING"] = "false"
        os.environ["LANGCHAIN_ENDPOINT"] = ""
        os.environ["LANGCHAIN_API_KEY"] = ""
        
        # 禁用通用遥测
        os.environ["TELEMETRY_DISABLED"] = "true"
        os.environ["DO_NOT_TRACK"] = "1"
        os.environ["ANALYTICS_DISABLED"] = "true"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # 忽略额外的环境变量


# 全局配置实例
config = VectorServiceConfig()
