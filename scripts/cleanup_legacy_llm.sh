#!/bin/bash

# LLM工厂模式迁移后的旧代码清理脚本
# 执行前请确保所有服务已停止且代码已提交到Git

echo "🚀 开始清理旧的LLM代码..."

# 设置项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
cd "$PROJECT_ROOT"

echo "📍 当前目录: $PROJECT_ROOT"

# 确认操作
echo "⚠️  即将删除以下目录和文件:"
echo "   - src/llm/"
echo "   - services/analysis_service/llm/"  
echo "   - legacy/src/llm/"
echo ""
read -p "确认继续? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 1
fi

# 备份功能
BACKUP_DIR="./backup_llm_$(date +%Y%m%d_%H%M%S)"
echo "📦 创建备份目录: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 备份要删除的目录
echo "💾 备份旧代码..."
if [ -d "src/llm" ]; then
    cp -r "src/llm" "$BACKUP_DIR/src_llm_backup"
    echo "   ✅ 已备份 src/llm/"
fi

if [ -d "services/analysis_service/llm" ]; then
    cp -r "services/analysis_service/llm" "$BACKUP_DIR/analysis_service_llm_backup"
    echo "   ✅ 已备份 services/analysis_service/llm/"
fi

if [ -d "legacy/src/llm" ]; then
    cp -r "legacy/src/llm" "$BACKUP_DIR/legacy_src_llm_backup"
    echo "   ✅ 已备份 legacy/src/llm/"
fi

# 删除旧代码
echo "🗑️  删除旧代码..."

# 删除 src/llm/
if [ -d "src/llm" ]; then
    rm -rf "src/llm"
    echo "   ✅ 已删除 src/llm/"
else
    echo "   ℹ️  src/llm/ 不存在"
fi

# 删除 services/analysis_service/llm/
if [ -d "services/analysis_service/llm" ]; then
    rm -rf "services/analysis_service/llm"
    echo "   ✅ 已删除 services/analysis_service/llm/"
else
    echo "   ℹ️  services/analysis_service/llm/ 不存在"
fi

# 删除 legacy/src/llm/
if [ -d "legacy/src/llm" ]; then
    rm -rf "legacy/src/llm"
    echo "   ✅ 已删除 legacy/src/llm/"
else
    echo "   ℹ️  legacy/src/llm/ 不存在"
fi

# 检查是否有遗留的import引用
echo "🔍 检查遗留的import引用..."
echo "搜索可能需要更新的import语句:"

# 搜索可能的遗留引用
LEGACY_IMPORTS=$(grep -r "from src\.llm" . --include="*.py" 2>/dev/null || true)
if [ ! -z "$LEGACY_IMPORTS" ]; then
    echo "⚠️  发现可能需要更新的import:"
    echo "$LEGACY_IMPORTS"
fi

LEGACY_IMPORTS2=$(grep -r "from.*analysis_service\.llm" . --include="*.py" 2>/dev/null || true)
if [ ! -z "$LEGACY_IMPORTS2" ]; then
    echo "⚠️  发现可能需要更新的import:"
    echo "$LEGACY_IMPORTS2"
fi

# 验证shared/llm存在
if [ -d "shared/llm" ]; then
    echo "✅ 确认 shared/llm/ 存在"
    echo "   - 包含文件: $(ls shared/llm/)"
    echo "   - providers: $(ls shared/llm/providers/ 2>/dev/null || echo '无')"
else
    echo "❌ 错误: shared/llm/ 不存在！"
    exit 1
fi

# 建议后续操作
echo ""
echo "🎉 清理完成！"
echo ""
echo "📋 后续建议操作:"
echo "1. 运行测试验证功能正常:"
echo "   python issues/llm_factory_migration_test.py"
echo ""
echo "2. 检查服务启动是否正常:"
echo "   docker-compose up --build"
echo ""
echo "3. 如果一切正常，可以删除备份:"
echo "   rm -rf $BACKUP_DIR"
echo ""
echo "4. 提交变更到Git:"
echo "   git add ."
echo "   git commit -m 'Clean up legacy LLM code after migration to shared module'"
echo ""
echo "💾 备份位置: $BACKUP_DIR"
echo "   (如果需要恢复，请从此目录恢复)"

echo "✨ 清理脚本执行完毕！" 