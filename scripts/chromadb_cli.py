#!/usr/bin/env python3
"""
ChromaDB管理指南
现在使用ChromaDB原生CLI进行管理
"""


def main():
    """显示ChromaDB原生CLI使用指南"""
    print("🗄️  ChromaDB管理指南")
    print("=" * 50)
    print()
    print("现在使用ChromaDB原生CLI进行管理，常用命令：")
    print()
    print("📡 启动服务:")
    print("   chroma run --host 0.0.0.0 --port 8001 --path ./data/chromadb")
    print()
    print("🔍 健康检查:")
    print("   curl http://localhost:8001/api/v1/heartbeat")
    print()
    print("📊 查看集合:")
    print("   curl http://localhost:8001/api/v1/collections")
    print()
    print("🗑️  删除集合:")
    print("   curl -X DELETE http://localhost:8001/api/v1/collections/{collection_name}")
    print()
    print("💾 备份数据:")
    print("   cp -r ./data/chromadb ./backup/chromadb_$(date +%Y%m%d_%H%M%S)")
    print()
    print("📖 更多信息:")
    print("   chroma --help")
    print("   https://docs.trychroma.com/")
    print()


if __name__ == "__main__":
    main()
