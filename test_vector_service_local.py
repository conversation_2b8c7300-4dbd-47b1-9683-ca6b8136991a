#!/usr/bin/env python3
"""
测试修正后的vector_service本地ChromaDB模式
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "services" / "vector_service"))

from langchain_core.documents import Document
from services.vector_service.core.vector_manager import VectorManager
from services.vector_service.core.embeddings import BGEEmbeddingService
from shared.models.file_info import FileInfo, FileStatus
from shared.utils.logger import get_logger

logger = get_logger(__name__)


def test_vector_manager():
    """测试向量管理器"""
    try:
        logger.info("开始测试向量管理器...")
        
        # 初始化嵌入服务
        embedding_service = BGEEmbeddingService()
        logger.info("嵌入服务初始化完成")
        
        # 初始化向量管理器
        vector_manager = VectorManager(
            base_persist_dir="./test_cache/chroma_db",
            embedding_service=embedding_service
        )
        logger.info("向量管理器初始化完成")
        
        # 创建测试文档
        test_file_code = "test_001"
        test_documents = [
            Document(
                page_content="这是一个测试文档，包含风险分析相关内容。",
                metadata={"source": "test.txt", "chunk_id": 0}
            ),
            Document(
                page_content="客户的信用评分为750，属于良好信用等级。",
                metadata={"source": "test.txt", "chunk_id": 1}
            ),
            Document(
                page_content="月收入为15000元，负债率为30%，风险等级为中等。",
                metadata={"source": "test.txt", "chunk_id": 2}
            )
        ]
        
        # 创建文件信息
        file_info = FileInfo(
            file_code=test_file_code,
            file_name="test.txt",
            file_size=1024,
            status=FileStatus.PROCESSING
        )
        
        # 存储文档
        logger.info("开始存储文档...")
        success = vector_manager.store_documents(test_file_code, test_documents, file_info)
        
        if success:
            logger.info("✅ 文档存储成功")
            
            # 测试搜索
            logger.info("开始测试搜索...")
            search_results = vector_manager.search_documents(
                file_code=test_file_code,
                query="信用评分",
                top_k=2
            )
            
            logger.info(f"搜索结果: {search_results}")
            
            # 测试统计信息
            stats = vector_manager.get_collection_stats(test_file_code)
            logger.info(f"集合统计: {stats}")
            
            # 测试健康检查
            health = vector_manager.health_check()
            logger.info(f"健康检查: {health}")
            
            logger.info("✅ 所有测试通过")
            
        else:
            logger.error("❌ 文档存储失败")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_vector_manager()
