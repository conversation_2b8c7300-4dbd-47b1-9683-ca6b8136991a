"""
风险分析任务
处理风险分析的Celery任务（非流式模式）
"""

import time
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

from shared.celery_config import celery_app
from shared.utils.logger import get_logger
from shared.models.analysis_result import AnalysisResult, AnalysisStatus
from services.worker_service.core.analyzer import RiskAnalyzer

logger = get_logger(__name__)


@celery_app.task(bind=True, name='worker.tasks.analyze_risk')
def analyze_risk(
    self,
    file_code: str,
    analysis_type: str = "default",
    options: Optional[Dict[str, Any]] = None,
    priority: int = 1
):
    """
    风险分析任务（非流式模式）
    
    Args:
        file_code: 文件编码
        analysis_type: 分析类型
        options: 分析选项
        priority: 优先级
        
    Returns:
        分析结果
    """
    start_time = time.time()
    analyzer = None
    
    try:
        logger.info(f"开始风险分析任务: {file_code}, 类型: {analysis_type} (任务ID: {self.request.id})")
        
        # 更新任务进度
        self.update_state(
            state='STARTED',
            meta={
                'stage': '初始化',
                'progress': 0,
                'file_code': file_code,
                'analysis_type': analysis_type,
                'started_at': datetime.now().isoformat()
            }
        )
        
        # 创建风险分析器
        analyzer = RiskAnalyzer()
        
        # 更新进度：开始分析
        self.update_state(
            state='PROGRESS',
            meta={
                'stage': '数据检索',
                'progress': 20,
                'file_code': file_code,
                'message': '正在检索相关数据'
            }
        )
        
        # 执行风险分析
        logger.info(f"开始执行风险分析: {file_code}")
        analysis_start = time.time()
        
        # 更新进度：LLM分析中
        self.update_state(
            state='PROGRESS',
            meta={
                'stage': 'LLM分析',
                'progress': 60,
                'file_code': file_code,
                'message': '正在进行风险评估分析'
            }
        )
        
        # 使用asyncio.run()执行异步分析
        analysis_result = asyncio.run(analyzer.analyze_risk(
            file_code=file_code,
            analysis_type=analysis_type,
            options=options or {}
        ))
        
        analysis_time = time.time() - analysis_start
        total_time = time.time() - start_time
        
        if analysis_result.status == AnalysisStatus.COMPLETED:
            # 更新进度：完成
            self.update_state(
                state='SUCCESS',
                meta={
                    'stage': '完成',
                    'progress': 100,
                    'file_code': file_code,
                    'analysis_type': analysis_type,
                    'analysis_time': analysis_time,
                    'total_time': total_time,
                    'completed_at': datetime.now().isoformat(),
                    'message': '风险分析完成'
                }
            )
            
            logger.info(f"风险分析任务完成: {file_code}, 总耗时 {total_time:.2f}s")
            
            # 返回分析结果
            return {
                'success': True,
                'analysis_result': analysis_result.dict(),
                'processing_time': total_time,
                'analysis_time': analysis_time,
                'message': '风险分析成功'
            }
        else:
            # 分析失败
            raise Exception(f"风险分析失败: {analysis_result.error_message}")
            
    except Exception as e:
        error_msg = str(e)
        total_time = time.time() - start_time
        
        logger.error(f"风险分析任务失败 {file_code} (任务ID: {self.request.id}, 耗时 {total_time:.2f}s): {error_msg}")
        
        # 更新任务状态为失败
        self.update_state(
            state='FAILURE',
            meta={
                'stage': '失败',
                'progress': 0,
                'file_code': file_code,
                'analysis_type': analysis_type,
                'error': error_msg,
                'total_time': total_time,
                'failed_at': datetime.now().isoformat()
            }
        )
        
        # 重新抛出异常，让Celery处理
        raise
        
    finally:
        # 清理资源
        if analyzer:
            try:
                asyncio.run(analyzer.cleanup())
            except Exception as cleanup_error:
                logger.warning(f"清理风险分析器资源时出错: {cleanup_error}")


@celery_app.task(bind=True, name='worker.tasks.batch_analyze_risk')
def batch_analyze_risk(
    self,
    file_codes: list,
    analysis_type: str = "default",
    options: Optional[Dict[str, Any]] = None
):
    """
    批量风险分析任务

    Args:
        file_codes: 文件编码列表
        analysis_type: 分析类型
        options: 分析选项

    Returns:
        批量分析结果
    """
    
    async def _run_batch_analysis():
        """异步批量分析的内部函数"""
        start_time = time.time()
        
        logger.info(f"开始批量风险分析任务: {len(file_codes)} 个文件 (任务ID: {self.request.id})")

        # 更新任务进度
        self.update_state(
            state='STARTED',
            meta={
                'stage': '初始化',
                'progress': 0,
                'total_files': len(file_codes),
                'started_at': datetime.now().isoformat()
            }
        )

        results = []
        analyzer = RiskAnalyzer()

        try:
            for i, file_code in enumerate(file_codes):
                try:
                    progress = int((i / len(file_codes)) * 100)

                    # 更新进度
                    self.update_state(
                        state='PROGRESS',
                        meta={
                            'stage': '批量分析',
                            'progress': progress,
                            'current_file': i + 1,
                            'total_files': len(file_codes),
                            'current_file_code': file_code,
                            'message': f'正在分析第 {i + 1}/{len(file_codes)} 个文件'
                        }
                    )

                    # 执行单个文件分析
                    analysis_result = await analyzer.analyze_risk(
                        file_code=file_code,
                        analysis_type=analysis_type,
                        options=options or {}
                    )

                    results.append({
                        'file_code': file_code,
                        'success': analysis_result.status == AnalysisStatus.COMPLETED,
                        'analysis_result': analysis_result.dict() if analysis_result.status == AnalysisStatus.COMPLETED else None,
                        'error': analysis_result.error_message if analysis_result.status == AnalysisStatus.FAILED else None
                    })

                    logger.info(f"文件分析完成: {file_code} ({i + 1}/{len(file_codes)})")

                except Exception as e:
                    logger.error(f"文件分析失败: {file_code}: {e}")
                    results.append({
                        'file_code': file_code,
                        'success': False,
                        'error': str(e)
                    })

            total_time = time.time() - start_time
            successful_count = len([r for r in results if r['success']])

            # 完成
            self.update_state(
                state='SUCCESS',
                meta={
                    'stage': '完成',
                    'progress': 100,
                    'total_files': len(file_codes),
                    'successful_count': successful_count,
                    'failed_count': len(file_codes) - successful_count,
                    'total_time': total_time,
                    'completed_at': datetime.now().isoformat(),
                    'message': f'批量分析完成: {successful_count}/{len(file_codes)} 成功'
                }
            )

            logger.info(f"批量风险分析任务完成: {successful_count}/{len(file_codes)} 成功, 总耗时 {total_time:.2f}s")

            return {
                'success': True,
                'results': results,
                'summary': {
                    'total_files': len(file_codes),
                    'successful_count': successful_count,
                    'failed_count': len(file_codes) - successful_count,
                    'success_rate': successful_count / len(file_codes) * 100,
                    'processing_time': total_time
                },
                'message': f'批量分析完成: {successful_count}/{len(file_codes)} 成功'
            }
            
        finally:
            # 清理资源
            try:
                await analyzer.cleanup()
            except Exception as cleanup_error:
                logger.warning(f"清理风险分析器资源时出错: {cleanup_error}")
    
    # 使用asyncio.run()执行异步批量分析
    try:
        return asyncio.run(_run_batch_analysis())
    except Exception as e:
        error_msg = str(e)
        total_time = time.time() - time.time()  # 这里需要记录开始时间
        
        logger.error(f"批量风险分析任务失败 (任务ID: {self.request.id}): {error_msg}")
        
        # 更新任务状态为失败
        self.update_state(
            state='FAILURE',
            meta={
                'stage': '失败',
                'error': error_msg,
                'total_files': len(file_codes),
                'total_time': total_time,
                'failed_at': datetime.now().isoformat()
            }
        )
        
        raise
