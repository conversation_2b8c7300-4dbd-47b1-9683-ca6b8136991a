#!/bin/bash

# Docker环境启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 显示帮助信息
show_help() {
    echo "Deep Risk RAG Docker启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV        环境类型 (dev|prod) [默认: dev]"
    echo "  -d, --detach         后台运行"
    echo "  -b, --build          强制重新构建镜像"
    echo "  -p, --pull           拉取最新基础镜像"
    echo "  -c, --clean          清理旧容器和镜像"
    echo "  -s, --stop           停止所有服务"
    echo "  -l, --logs           显示服务日志"
    echo "  -h, --help           显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                   # 启动开发环境"
    echo "  $0 -e prod -d        # 后台启动生产环境"
    echo "  $0 -b                # 重新构建并启动"
    echo "  $0 -s                # 停止所有服务"
    echo "  $0 -l                # 查看日志"
}

# 检查Docker和Docker Compose
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi

    print_message "Docker环境检查通过"
}

# 检查环境变量
check_env_vars() {
    if [ -z "$DEEPSEEK_API_KEY" ]; then
        print_warning "DEEPSEEK_API_KEY环境变量未设置"
        print_warning "请设置: export DEEPSEEK_API_KEY=your_api_key"
    fi

    # 检查.env文件
    if [ ! -f ".env" ]; then
        print_warning ".env文件不存在，将使用默认配置"
    else
        print_message "发现.env配置文件"
    fi
}

# 清理旧容器和镜像
clean_docker() {
    print_header "🧹 清理Docker资源..."
    
    # 停止相关容器
    docker-compose -f docker/docker-compose.yml down --remove-orphans 2>/dev/null || true
    docker-compose -f docker/docker-compose.dev.yml down --remove-orphans 2>/dev/null || true
    
    # 清理悬空镜像
    docker image prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    print_message "Docker资源清理完成"
}

# 拉取最新镜像
pull_images() {
    print_header "📥 拉取最新基础镜像..."
    
    docker pull python:3.11-slim
    docker pull redis:7-alpine
    docker pull chromadb/chroma:latest
    docker pull mher/flower:0.9.7
    
    if [ "$ENV" = "dev" ]; then
        docker pull rediscommander/redis-commander:latest
    fi
    
    print_message "镜像拉取完成"
}

# 构建镜像
build_images() {
    print_header "🔨 构建服务镜像..."
    
    if [ "$ENV" = "dev" ]; then
        docker-compose -f docker/docker-compose.dev.yml build
    else
        docker-compose -f docker/docker-compose.yml build
    fi
    
    print_message "镜像构建完成"
}

# 启动服务
start_services() {
    print_header "🚀 启动Deep Risk RAG服务..."
    
    local compose_file
    if [ "$ENV" = "dev" ]; then
        compose_file="docker/docker-compose.dev.yml"
        print_message "启动开发环境..."
    else
        compose_file="docker/docker-compose.yml"
        print_message "启动生产环境..."
    fi
    
    local docker_cmd="docker-compose -f $compose_file up"
    
    if [ "$DETACH" = "true" ]; then
        docker_cmd="$docker_cmd -d"
    fi
    
    $docker_cmd
    
    if [ "$DETACH" = "true" ]; then
        print_message "服务已在后台启动"
        show_service_info
    fi
}

# 停止服务
stop_services() {
    print_header "🛑 停止Deep Risk RAG服务..."
    
    docker-compose -f docker/docker-compose.yml down --remove-orphans 2>/dev/null || true
    docker-compose -f docker/docker-compose.dev.yml down --remove-orphans 2>/dev/null || true
    
    print_message "所有服务已停止"
}

# 显示日志
show_logs() {
    print_header "📋 显示服务日志..."
    
    local compose_file
    if [ "$ENV" = "dev" ]; then
        compose_file="docker/docker-compose.dev.yml"
    else
        compose_file="docker/docker-compose.yml"
    fi
    
    docker-compose -f $compose_file logs -f
}

# 显示服务信息
show_service_info() {
    print_header "📊 服务访问信息:"
    echo ""
    echo "🌐 统一Web服务:     http://localhost:8000"
    echo "📖 API文档:         http://localhost:8000/docs"
    echo "🗄️  ChromaDB:       http://localhost:8001"
    echo "🌸 Flower监控:      http://localhost:5555"
    
    if [ "$ENV" = "dev" ]; then
        echo "🔴 Redis管理:       http://localhost:8081"
    fi
    
    echo ""
    echo "📋 健康检查:"
    echo "   curl http://localhost:8000/health"
    echo "   curl http://localhost:8001/api/v1/heartbeat"
    echo ""
}

# 默认参数
ENV="dev"
DETACH="false"
BUILD="false"
PULL="false"
CLEAN="false"
STOP="false"
LOGS="false"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -d|--detach)
            DETACH="true"
            shift
            ;;
        -b|--build)
            BUILD="true"
            shift
            ;;
        -p|--pull)
            PULL="true"
            shift
            ;;
        -c|--clean)
            CLEAN="true"
            shift
            ;;
        -s|--stop)
            STOP="true"
            shift
            ;;
        -l|--logs)
            LOGS="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [ "$ENV" != "dev" ] && [ "$ENV" != "prod" ]; then
    print_error "无效的环境类型: $ENV (支持: dev, prod)"
    exit 1
fi

# 主逻辑
main() {
    print_header "🎯 Deep Risk RAG Docker管理脚本"
    echo ""
    
    # 检查Docker环境
    check_docker
    
    # 如果是停止命令
    if [ "$STOP" = "true" ]; then
        stop_services
        exit 0
    fi
    
    # 如果是查看日志
    if [ "$LOGS" = "true" ]; then
        show_logs
        exit 0
    fi
    
    # 检查环境变量
    check_env_vars
    
    # 清理（如果需要）
    if [ "$CLEAN" = "true" ]; then
        clean_docker
    fi
    
    # 拉取镜像（如果需要）
    if [ "$PULL" = "true" ]; then
        pull_images
    fi
    
    # 构建镜像（如果需要）
    if [ "$BUILD" = "true" ]; then
        build_images
    fi
    
    # 启动服务
    start_services
}

# 执行主函数
main
