"""
ChromaDB上下文管理器
确保向量存储资源的正确管理和释放
"""

import logging
from pathlib import Path
from typing import Optional, Any, Dict
from contextlib import contextmanager

from langchain_chroma import Chroma
from shared.utils.windows_file_utils import force_garbage_collection, is_windows

logger = logging.getLogger(__name__)


class ChromaVectorStoreContext:
    """ChromaDB向量存储上下文管理器"""
    
    def __init__(
        self,
        collection_name: str,
        embedding_function: Any,
        persist_directory: str,
        auto_cleanup: bool = True
    ):
        self.collection_name = collection_name
        self.embedding_function = embedding_function
        self.persist_directory = persist_directory
        self.auto_cleanup = auto_cleanup
        self.vector_store: Optional[Chroma] = None
        self._is_closed = False
        
    def __enter__(self) -> Chroma:
        """进入上下文时创建向量存储"""
        try:
            # 确保持久化目录存在
            Path(self.persist_directory).mkdir(parents=True, exist_ok=True)
            
            # 创建向量存储实例
            self.vector_store = Chroma(
                collection_name=self.collection_name,
                embedding_function=self.embedding_function,
                persist_directory=self.persist_directory,
            )
            
            logger.debug(f"Created ChromaDB vector store: {self.collection_name}")
            return self.vector_store
            
        except Exception as e:
            logger.error(f"Failed to create ChromaDB vector store: {e}")
            self._cleanup_resources()
            raise
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文时清理资源"""
        self._cleanup_resources()
        
        # 如果有异常且需要自动清理，记录异常信息
        if exc_type is not None:
            logger.warning(
                f"ChromaDB context exited with exception: {exc_type.__name__}: {exc_val}"
            )
        
        return False  # 不抑制异常
    
    def _cleanup_resources(self):
        """清理ChromaDB资源"""
        if self._is_closed:
            return
            
        try:
            if self.vector_store is not None:
                # 优化：简化ChromaDB连接关闭逻辑
                self._close_chroma_client()
                
                # 安全地清理内部引用
                self._safe_clear_internal_refs()
                
                # 清理向量存储引用
                self.vector_store = None
            
            # 在Windows上强制垃圾回收
            if is_windows():
                force_garbage_collection()
                
            logger.debug(f"Cleaned up ChromaDB resources for: {self.collection_name}")
            
        except Exception as e:
            logger.warning(f"Error during ChromaDB cleanup: {e}")
        finally:
            self._is_closed = True
    
    def _safe_clear_internal_refs(self):
        """安全地清理内部引用，避免只读属性错误"""
        try:
            # 尝试清理集合引用（如果不是只读属性）
            if hasattr(self.vector_store, '_collection'):
                try:
                    # 检查是否为property（只读）
                    if isinstance(type(self.vector_store).__dict__.get('_collection'), property):
                        logger.debug("_collection is a property, skipping direct assignment")
                    else:
                        self.vector_store._collection = None
                        logger.debug("Cleared _collection reference")
                except (AttributeError, TypeError) as e:
                    logger.debug(f"Cannot clear _collection: {e}")
                    
            # 清理其他可能的内部引用
            attrs_to_clear = ['_client_settings', '_embedding_function']
            for attr in attrs_to_clear:
                if hasattr(self.vector_store, attr):
                    try:
                        if not isinstance(type(self.vector_store).__dict__.get(attr), property):
                            setattr(self.vector_store, attr, None)
                            logger.debug(f"Cleared {attr} reference")
                    except (AttributeError, TypeError):
                        pass  # 静默忽略只读属性
                        
        except Exception as e:
            logger.debug(f"Error during internal reference cleanup: {e}")
    
    def _close_chroma_client(self):
        """优化：统一的ChromaDB客户端关闭逻辑"""
        if not hasattr(self.vector_store, '_client'):
            return
            
        try:
            client = self.vector_store._client
            
            # 尝试多种关闭方法
            close_methods = ['close', 'shutdown', 'disconnect']
            for method_name in close_methods:
                if hasattr(client, method_name):
                    getattr(client, method_name)()
                    logger.debug(f"Closed ChromaDB client using {method_name}")
                    break
                    
            # 处理嵌套客户端
            if hasattr(client, '_client'):
                nested_client = client._client
                for method_name in close_methods:
                    if hasattr(nested_client, method_name):
                        getattr(nested_client, method_name)()
                        logger.debug(f"Closed nested ChromaDB client using {method_name}")
                        break
                        
        except Exception as e:
            logger.debug(f"Error closing ChromaDB client: {e}")


@contextmanager
def temporary_vector_store(
    collection_name: str,
    embedding_function: Any,
    persist_directory: str,
    auto_cleanup: bool = True
):
    """
    临时向量存储上下文管理器
    
    Args:
        collection_name: 集合名称
        embedding_function: 嵌入函数
        persist_directory: 持久化目录
        auto_cleanup: 是否自动清理
        
    Yields:
        Chroma向量存储实例
    """
    context = ChromaVectorStoreContext(
        collection_name=collection_name,
        embedding_function=embedding_function,
        persist_directory=persist_directory,
        auto_cleanup=auto_cleanup
    )
    
    with context as vector_store:
        yield vector_store


class ChromaHealthChecker:
    """ChromaDB健康检查器"""
    
    def __init__(self, embedding_function: Any, base_persist_dir: str):
        self.embedding_function = embedding_function
        self.base_persist_dir = Path(base_persist_dir)
        
    def check_vector_storage_health(self) -> Dict[str, Any]:
        """
        检查向量存储健康状态
        
        Returns:
            健康检查结果
        """
        test_code = "health_check_test"
        test_collection = f"test_collection_{test_code}"
        test_persist_dir = self.base_persist_dir / test_code
        
        result = {
            "success": False,
            "error": None,
            "cleanup_success": False
        }
        
        try:
            # 使用上下文管理器创建临时向量存储
            with temporary_vector_store(
                collection_name=test_collection,
                embedding_function=self.embedding_function,
                persist_directory=str(test_persist_dir)
            ) as test_vector_store:
                
                # 测试基本操作
                if test_vector_store is not None:
                    # 尝试获取集合信息
                    collection = test_vector_store._collection
                    if collection is not None:
                        result["success"] = True
                        logger.debug("ChromaDB health check passed")
                    else:
                        result["error"] = "Unable to access collection"
                else:
                    result["error"] = "Unable to create vector store"
            
            # 上下文管理器会自动清理资源
            # 额外清理测试目录
            result["cleanup_success"] = self._cleanup_test_directory(test_persist_dir)
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"ChromaDB health check failed: {e}")
            
            # 即使出错也要尝试清理
            try:
                result["cleanup_success"] = self._cleanup_test_directory(test_persist_dir)
            except Exception as cleanup_error:
                logger.warning(f"Cleanup after health check error failed: {cleanup_error}")
        
        return result
    
    def _cleanup_test_directory(self, test_dir: Path) -> bool:
        """
        清理测试目录
        
        Args:
            test_dir: 测试目录路径
            
        Returns:
            是否清理成功
        """
        if not test_dir.exists():
            return True
        
        try:
            # 使用Windows兼容的删除方法
            from shared.utils.windows_file_utils import safe_remove_directory
            
            success = safe_remove_directory(test_dir, max_retries=3, retry_delay=0.5)
            if success:
                logger.debug(f"Successfully cleaned up test directory: {test_dir}")
            else:
                logger.warning(f"Failed to clean up test directory: {test_dir}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error cleaning up test directory {test_dir}: {e}")
            return False 