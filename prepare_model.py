# from huggingface_hub import snapshot_download

# print("开始下载 BAAI/bge-m3 模型，将忽略所有 .bin 文件...")
#
# # 使用 snapshot_download 函数，它可以精细地控制下载过程
# # repo_id: 指定要下载的模型
# # ignore_patterns: 设置一个忽略规则列表，这里我们忽略所有以 .bin 结尾的文件
# snapshot_download(
#     repo_id="BAAI/bge-m3",
#     ignore_patterns=["*.bin"]
# )
#
# print("模型文件已成功下载到本地缓存，且不包含 .bin 文件。")


from huggingface_hub import snapshot_download
import os

# 定义一个你自己的、全新的文件夹路径来存放纯净的模型
# 它可以是项目目录下的一个子文件夹，或任何你指定的位置
local_model_path = "/Users/<USER>/VsCodeProjects/deep-risk-rag/models/bge-m3-safetensors-only"

# 检查文件夹是否已存在，如果不存在则创建
if not os.path.exists(local_model_path):
    print(f"指定的本地路径不存在，将创建文件夹: {local_model_path}")
    os.makedirs(local_model_path)

print(f"开始下载一个纯净的 bge-m3副本到: {local_model_path}")
print("这个副本将不包含任何 .bin 文件。")

# 使用 local_dir 参数将模型下载到指定文件夹
snapshot_download(
    repo_id="BAAI/bge-m3",
    ignore_patterns=["*.bin"],
    local_dir=local_model_path,
    # local_dir_use_symlinks=False  # 建议设为False，避免使用符号链接
)

print(f"纯净的模型副本已准备就绪，存放于: {os.path.abspath(local_model_path)}")