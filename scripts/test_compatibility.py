#!/usr/bin/env python3
"""
系统兼容性测试脚本
验证不同操作系统下的 Worker 配置是否正确
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.system_compatibility import (
    get_system_config, 
    get_celery_worker_args,
    get_optimal_device,
    should_force_cpu,
    get_memory_strategy
)
from shared.celery_config import get_worker_startup_args


def test_system_compatibility():
    """测试系统兼容性配置"""
    print("=" * 60)
    print("🧪 系统兼容性测试")
    print("=" * 60)
    
    # 获取系统配置
    config = get_system_config()
    
    print(f"🖥️  系统信息:")
    print(f"   系统名称: {config.system_name}")
    print(f"   系统版本: {config.system_version}")
    print()
    
    print(f"🏊 Worker 配置:")
    print(f"   池类型: {config.pool_type}")
    print(f"   并发数: {config.concurrency}")
    print(f"   任务重启阈值: {config.max_tasks_per_child}")
    print(f"   启动方式: {config.multiprocessing_start_method}")
    print()
    
    print(f"🔧 设备配置:")
    print(f"   推荐设备: {config.device}")
    print(f"   强制CPU: {config.force_cpu}")
    print()
    
    print(f"🧠 内存管理:")
    print(f"   内存策略: {config.memory_strategy}")
    print()
    
    print(f"🌍 环境变量:")
    if config.env_vars:
        for key, value in config.env_vars.items():
            print(f"   {key}={value}")
    else:
        print("   无特殊环境变量")
    print()
    
    # 测试函数调用
    print(f"📋 函数测试:")
    print(f"   get_optimal_device(): {get_optimal_device()}")
    print(f"   should_force_cpu(): {should_force_cpu()}")
    print(f"   get_memory_strategy(): {get_memory_strategy()}")
    print()
    
    # 测试 Celery 参数
    print(f"🚀 Celery Worker 参数:")
    celery_args = get_celery_worker_args()
    for key, value in celery_args.items():
        print(f"   {key}: {value}")
    print()
    
    # 测试启动参数
    print(f"🎯 Worker 启动参数:")
    startup_args = get_worker_startup_args()
    for key, value in startup_args.items():
        print(f"   {key}: {value}")
    print()
    
    # 生成启动命令
    print(f"💻 推荐启动命令:")
    cmd_parts = ["celery", "-A", "services.worker_service.worker", "worker"]
    for key, value in startup_args.items():
        if value is not None:
            if key in ["pool", "concurrency", "loglevel"]:
                cmd_parts.extend([f"--{key}", str(value)])
            elif key == "max_tasks_per_child":
                cmd_parts.extend([f"--max-tasks-per-child", str(value)])
            elif key == "prefetch_multiplier":
                cmd_parts.extend([f"--prefetch-multiplier", str(value)])
    
    print(f"   {' '.join(cmd_parts)}")
    print()
    
    print("=" * 60)
    print("✅ 系统兼容性测试完成")
    print("=" * 60)


def test_health_check():
    """测试健康检查任务"""
    print("🏥 测试健康检查任务...")
    
    try:
        from services.worker_service.tasks.health import health_check
        
        print("   执行健康检查任务...")
        result = health_check.apply()
        task_result = result.get(timeout=30)
        
        print(f"   ✅ 健康检查执行成功")
        print(f"   整体健康状态: {task_result.get('overall_healthy', False)}")
        print(f"   检查耗时: {task_result.get('check_time', 0):.2f}秒")
        
        # 显示各组件状态
        health_status = task_result.get('health_status', {})
        for component, status in health_status.items():
            healthy = status.get('healthy', False)
            status_icon = "✅" if healthy else "❌"
            print(f"   {status_icon} {component}: {'健康' if healthy else '异常'}")
            
            if not healthy and 'error' in status:
                print(f"      错误: {status['error']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 健康检查执行失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 开始系统兼容性和功能测试")
    print()
    
    # 测试系统兼容性
    test_system_compatibility()
    
    # 测试健康检查
    print()
    health_ok = test_health_check()
    
    print()
    if health_ok:
        print("🎉 所有测试通过！系统兼容性配置正常工作。")
    else:
        print("⚠️  健康检查测试失败，但系统兼容性配置正常。")
        print("   这可能是由于缺少配置（如API密钥）或服务未启动导致的。")
    
    print()
    print("📝 注意事项:")
    print("   - macOS 系统会自动使用线程池避免 MPS+fork 冲突")
    print("   - Linux/Windows 系统会使用进程池享受进程隔离优势")
    print("   - 设备选择会根据系统兼容性自动调整")
    print("   - 内存管理策略会根据池类型自动优化")


if __name__ == "__main__":
    main()
