"""
RAG 链 - 集成 DeepSeek API 的检索增强生成系统
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import logging
from datetime import datetime

from langchain_core.documents import Document
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from config import settings
    from src.embeddings import GTELargeEmbeddings
    from src.vector_store import ChromaVectorStore, VectorStoreManager
    from src.llm import create_llm_from_settings, LLMError
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.error(f"导入配置失败: {e}")

    # 使用默认配置
    class DefaultSettings:
        DEEPSEEK_API_KEY = None
        DEEPSEEK_MODEL = "deepseek-reasoner"
        TEMPERATURE = 0.1
        MAX_TOKENS = 2000
        MAX_CONTEXT_LENGTH = 4000
        RETRIEVAL_TOP_K = 5

    settings = DefaultSettings()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DeepSeekRAGChain:
    """基于 DeepSeek 的 RAG 问答链"""

    def __init__(
        self,
        vector_store: Optional[ChromaVectorStore] = None,
        api_key: Optional[str] = None,
        model_name: str = None,
        temperature: float = None,
        max_tokens: int = None,
        **kwargs,
    ):
        """
        初始化 RAG 链

        Args:
            vector_store: 向量存储实例
            api_key: DeepSeek API 密钥
            model_name: 模型名称
            temperature: 生成温度
            max_tokens: 最大生成长度
        """
        # 初始化向量存储
        if vector_store is None:
            logger.info("未提供向量存储，创建默认实例")
            self.vector_store = ChromaVectorStore()
        else:
            self.vector_store = vector_store

        # 配置 LLM（保持向后兼容）
        self.api_key = (
            api_key or settings.DEEPSEEK_API_KEY or os.getenv("DEEPSEEK_API_KEY")
        )
        self.model_name = model_name or settings.DEEPSEEK_MODEL
        self.temperature = (
            temperature if temperature is not None else settings.TEMPERATURE
        )
        self.max_tokens = max_tokens or settings.MAX_TOKENS

        # 初始化 LLM（使用新的工厂模式，使用LangChain适配器）
        try:
            self.llm = create_llm_from_settings(settings, instance_key="rag_chain", langchain_compatible=True)
            # 同时保存原始LLM用于直接调用
            self._raw_llm = create_llm_from_settings(settings, instance_key="rag_chain_raw", langchain_compatible=False)
            logger.info(f"✅ 使用LLM提供商: {settings.LLM_PROVIDER}")
        except LLMError as e:
            logger.error(f"LLM初始化失败: {e}")
            raise ValueError(f"LLM初始化失败: {e}") from e

        # 初始化检索器
        self.retriever = self.vector_store.as_retriever(
            search_kwargs={"k": settings.RETRIEVAL_TOP_K}
        )

        # 创建提示模板
        self._create_prompts()

        # 创建 RAG 链
        self._create_chain()

        logger.info("✅ DeepSeek RAG 链初始化完成")
        logger.info(f"模型: {self.model_name}")
        logger.info(f"温度: {self.temperature}")
        logger.info(f"最大长度: {self.max_tokens}")

    def _create_prompts(self):
        """创建提示模板"""
        # 系统提示
        self.system_prompt = """你是一个专业的AI助手，专门基于提供的文档内容回答用户问题。

请遵循以下规则：
1. 仅基于提供的上下文文档回答问题
2. 如果文档中没有相关信息，请明确说明"根据提供的文档，我无法找到相关信息"
3. 回答要准确、简洁、有条理
4. 如果可能，请引用具体的文档片段
5. 保持客观中性的语调

上下文文档：
{context}

用户问题：{question}

请基于上述文档内容回答用户问题："""

        # 创建聊天提示模板
        self.prompt_template = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "你是一个专业的AI助手，专门基于提供的文档内容回答用户问题。",
                ),
                ("human", self.system_prompt),
            ]
        )

        # 创建简单的提示模板（用于格式化上下文）
        self.context_prompt = PromptTemplate(
            input_variables=["context", "question"], template=self.system_prompt
        )

    def _format_documents(self, documents: List[Document]) -> str:
        """
        格式化文档为上下文字符串

        Args:
            documents: 文档列表

        Returns:
            格式化的上下文字符串
        """
        if not documents:
            return "没有找到相关文档。"

        context_parts = []
        for i, doc in enumerate(documents, 1):
            # 获取文档元数据
            source = doc.metadata.get("source", "未知来源")
            file_name = doc.metadata.get("file_name", "未知文件")

            # 格式化文档内容
            context_part = f"""
文档 {i}:
来源: {file_name}
内容: {doc.page_content.strip()}
---"""
            context_parts.append(context_part)

        return "\n".join(context_parts)

    def _create_chain(self):
        """创建 RAG 链"""

        # 定义检索和格式化函数
        def retrieve_and_format(query: str) -> str:
            """检索并格式化文档"""
            documents = self.retriever.invoke(query)
            return self._format_documents(documents)

        # 创建 RAG 链
        self.rag_chain = (
            RunnableParallel(
                {
                    "context": lambda x: retrieve_and_format(x["question"]),
                    "question": RunnablePassthrough(),
                }
            )
            | RunnablePassthrough.assign(context=lambda x: x["context"])
            | self.context_prompt
            | self.llm
            | StrOutputParser()
        )

        logger.info("RAG 链创建完成")

    def ask(
        self,
        question: str,
        include_sources: bool = True,
        max_context_length: int = None,
    ) -> Dict[str, Any]:
        """
        提问并获取答案

        Args:
            question: 用户问题
            include_sources: 是否包含来源文档
            max_context_length: 最大上下文长度

        Returns:
            包含答案和元数据的字典
        """
        if not question.strip():
            return {"answer": "请提供一个有效的问题。", "sources": [], "success": False}

        try:
            logger.info(f"处理问题: {question[:100]}...")

            # 检查是否启用文档检索
            if not settings.ENABLE_DOCUMENT_RETRIEVAL:
                logger.info("文档检索已禁用，使用纯LLM模式")
                # 直接使用LLM回答，不进行文档检索
                answer = self._raw_llm.invoke(f"请回答以下问题：{question}")
                return {
                    "answer": answer.content.strip(),
                    "sources": [],
                    "success": True,
                    "retrieved_docs_count": 0,
                    "question": question,
                    "timestamp": datetime.now().isoformat(),
                    "mode": "pure_llm"
                }

            # 检索相关文档
            retrieved_docs = self.retriever.invoke(question)

            if not retrieved_docs:
                return {
                    "answer": "抱歉，我在知识库中没有找到与您问题相关的信息。请尝试重新表述您的问题或检查知识库是否包含相关内容。",
                    "sources": [],
                    "success": True,
                    "retrieved_docs_count": 0,
                    "question": question,
                    "timestamp": datetime.now().isoformat(),
                    "mode": "rag_no_docs"
                }

            # 限制上下文长度
            if max_context_length is None:
                max_context_length = settings.MAX_CONTEXT_LENGTH

            # 截断过长的上下文
            context = self._format_documents(retrieved_docs)
            if len(context) > max_context_length:
                context = context[:max_context_length] + "\n...(内容已截断)"
                logger.warning(f"上下文过长，已截断到 {max_context_length} 字符")

            # 生成答案
            answer = self.rag_chain.invoke({"question": question})

            # 准备来源信息
            sources = []
            if include_sources:
                for doc in retrieved_docs:
                    source_info = {
                        "file_name": doc.metadata.get("file_name", "未知文件"),
                        "source": doc.metadata.get("source", "未知来源"),
                        "content_preview": (
                            doc.page_content[:200] + "..."
                            if len(doc.page_content) > 200
                            else doc.page_content
                        ),
                        "metadata": doc.metadata,
                    }
                    sources.append(source_info)

            result = {
                "answer": answer.strip(),
                "sources": sources,
                "success": True,
                "retrieved_docs_count": len(retrieved_docs),
                "question": question,
                "timestamp": datetime.now().isoformat(),
                "mode": "rag_enabled"
            }

            logger.info(f"✅ 问题处理完成，检索到 {len(retrieved_docs)} 个相关文档")
            return result

        except Exception as e:
            logger.error(f"问答处理失败: {e}")
            return {
                "answer": f"抱歉，处理您的问题时出现了错误: {str(e)}",
                "sources": [],
                "success": False,
                "error": str(e),
            }

    def batch_ask(
        self, questions: List[str], include_sources: bool = False
    ) -> List[Dict[str, Any]]:
        """
        批量提问

        Args:
            questions: 问题列表
            include_sources: 是否包含来源

        Returns:
            答案列表
        """
        results = []
        for i, question in enumerate(questions, 1):
            logger.info(f"处理第 {i}/{len(questions)} 个问题")
            result = self.ask(question, include_sources=include_sources)
            results.append(result)

        return results

    def get_chain_info(self) -> Dict[str, Any]:
        """获取链信息"""
        vector_info = self.vector_store.get_collection_info()

        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "vector_store_info": vector_info,
            "retrieval_top_k": settings.RETRIEVAL_TOP_K,
        }


# 便捷函数
def create_rag_chain(
    vector_store: Optional[ChromaVectorStore] = None,
    api_key: Optional[str] = None,
    **kwargs,
) -> DeepSeekRAGChain:
    """
    创建 RAG 链实例

    Args:
        vector_store: 向量存储
        api_key: API 密钥
        **kwargs: 其他参数

    Returns:
        RAG 链实例
    """
    return DeepSeekRAGChain(vector_store=vector_store, api_key=api_key, **kwargs)
