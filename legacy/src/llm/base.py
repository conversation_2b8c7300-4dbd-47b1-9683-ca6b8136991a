"""
LLM基础接口定义
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass
from enum import Enum


class LLMProvider(Enum):
    """LLM提供商枚举"""

    DEEPSEEK = "deepseek"
    OPENAI = "openai"
    AZURE_OPENAI = "azure_openai"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"


@dataclass
class LLMConfig:
    """LLM配置数据类"""

    provider: LLMProvider
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: str = ""
    temperature: float = 0.1
    max_tokens: Optional[int] = None
    timeout: int = 60
    max_retries: int = 3
    # 扩展配置，用于特定提供商的额外参数
    extra_params: Dict[str, Any] = None

    def __post_init__(self):
        if self.extra_params is None:
            self.extra_params = {}


@dataclass
class LLMResponse:
    """LLM响应数据类"""

    content: str
    usage: Optional[Dict[str, Any]] = None
    model: Optional[str] = None
    finish_reason: Optional[str] = None
    raw_response: Optional[Any] = None


class BaseLLM(ABC):
    """LLM基础抽象类"""

    def __init__(self, config: LLMConfig):
        """
        初始化LLM实例

        Args:
            config: LLM配置
        """
        self.config = config
        self._client = None
        self._initialize_client()

    @abstractmethod
    def _initialize_client(self):
        """初始化LLM客户端"""
        pass

    @abstractmethod
    def invoke(self, prompt: str, **kwargs) -> LLMResponse:
        """
        调用LLM生成响应

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Returns:
            LLM响应
        """
        pass

    @abstractmethod
    def batch_invoke(self, prompts: List[str], **kwargs) -> List[LLMResponse]:
        """
        批量调用LLM

        Args:
            prompts: 提示列表
            **kwargs: 额外参数

        Returns:
            响应列表
        """
        pass

    @abstractmethod
    def stream_invoke(self, prompt: str, **kwargs):
        """
        流式调用LLM

        Args:
            prompt: 输入提示
            **kwargs: 额外参数

        Yields:
            流式响应片段
        """
        pass

    def get_provider(self) -> LLMProvider:
        """获取提供商类型"""
        return self.config.provider

    def get_model(self) -> str:
        """获取模型名称"""
        return self.config.model

    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        # 重新初始化客户端
        self._initialize_client()

    def _merge_call_params(self, **kwargs) -> Dict[str, Any]:
        """合并调用参数 - 减少重复代码"""
        return {
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
        }


class LLMError(Exception):
    """LLM相关错误基类"""

    pass


class LLMConnectionError(LLMError):
    """LLM连接错误"""

    pass


class LLMAuthenticationError(LLMError):
    """LLM认证错误"""

    pass


class LLMRateLimitError(LLMError):
    """LLM速率限制错误"""

    pass


class LLMInvalidRequestError(LLMError):
    """LLM无效请求错误"""

    pass
