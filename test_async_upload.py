#!/usr/bin/env python3
"""
测试异步上传功能
验证文件上传后是否立即返回，而不等待向量化完成
"""

import os
# 禁用PostHog和LangChain遥测数据发送
os.environ["POSTHOG_HOST"] = ""
os.environ["POSTHOG_PROJECT_ID"] = ""
os.environ["LANGCHAIN_TRACING_V2"] = "false"
os.environ["LANGCHAIN_ANALYTICS"] = "false"

import asyncio
import aiohttp
import time
import pandas as pd
import tempfile
from pathlib import Path


async def test_async_upload():
    """测试异步上传功能"""
    
    print("🚀 开始测试异步上传功能")
    print("=" * 60)
    
    # 创建测试CSV文件
    print("📄 创建测试CSV文件...")
    test_data = {
        '公司名称': ['公司A', '公司B', '公司C'] * 50,  # 创建150行数据
        '行业': ['科技', '金融', '制造'] * 50,
        '风险等级': ['高', '中', '低'] * 50,
        '描述': ['这是一个测试描述，包含较长的文本内容用于测试向量化处理时间'] * 150
    }
    
    df = pd.DataFrame(test_data)
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    df.to_csv(temp_file.name, index=False, encoding='utf-8')
    temp_file.close()
    
    print(f"✅ 测试文件创建完成: {Path(temp_file.name).name} ({len(df)} 行数据)")
    
    try:
        # 测试上传时间
        print("\n⏱️  测试上传响应时间...")
        
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            # 准备上传文件
            with open(temp_file.name, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field('file', f, filename='test_data.csv')
                
                # 发送上传请求
                async with session.post(
                    'http://localhost:8000/upload/',
                    data=data,
                    timeout=aiohttp.ClientTimeout(total=10)  # 10秒超时
                ) as response:
                    upload_time = time.time() - start_time
                    
                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ 上传成功! 响应时间: {upload_time:.2f} 秒")
                        print(f"   文件编码: {result.get('file_code')}")
                        print(f"   状态: {result.get('status')}")
                        print(f"   消息: {result.get('message')}")
                        
                        # 检查是否真的是异步（响应时间应该很短）
                        if upload_time < 3.0:  # 如果响应时间小于3秒，说明是异步的
                            print(f"🎉 异步上传成功! 上传立即返回，向量化在后台进行")
                        else:
                            print(f"⚠️  上传时间较长({upload_time:.2f}s)，可能仍然是同步处理")
                            
                        # 监控处理进度
                        file_code = result.get('file_code')
                        if file_code:
                            await monitor_processing_progress(session, file_code)
                            
                    else:
                        print(f"❌ 上传失败: {response.status}")
                        print(await response.text())
                        
    except asyncio.TimeoutError:
        print("❌ 上传超时! 说明仍然是同步处理")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file.name)
            print(f"🗑️  清理临时文件: {Path(temp_file.name).name}")
        except:
            pass


async def monitor_processing_progress(session, file_code):
    """监控处理进度"""
    print(f"\n🔍 监控处理进度: {file_code}")
    
    max_wait = 60  # 最多等待60秒
    check_interval = 3  # 每3秒检查一次
    
    for i in range(0, max_wait, check_interval):
        try:
            async with session.get(f'http://localhost:8000/status/{file_code}') as response:
                if response.status == 200:
                    result = await response.json()
                    status = result.get('status')
                    print(f"   {i+check_interval:2d}s: 状态 = {status}")
                    
                    if status == 'COMPLETED':
                        print(f"✅ 处理完成! 总用时: {i+check_interval} 秒")
                        break
                    elif status == 'FAILED':
                        print(f"❌ 处理失败!")
                        break
                else:
                    print(f"   查询状态失败: {response.status}")
                    
        except Exception as e:
            print(f"   查询异常: {e}")
            
        await asyncio.sleep(check_interval)
    else:
        print(f"⏰ 监控超时({max_wait}s)，处理可能仍在进行中")


async def test_service_availability():
    """测试服务可用性"""
    print("🏥 检查向量服务是否可用...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/health') as response:
                if response.status == 200:
                    print("✅ 向量服务可用")
                    return True
                else:
                    print(f"❌ 向量服务不可用: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ 无法连接到向量服务: {e}")
        print("💡 请确保向量服务已启动: python services/vector_service/main.py")
        return False


async def main():
    """主测试函数"""
    print("🧪 异步上传功能测试")
    print("=" * 60)
    
    # 检查服务可用性
    if not await test_service_availability():
        return
        
    # 执行异步上传测试
    await test_async_upload()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("- 如果上传响应时间 < 3秒，说明异步改进成功")
    print("- 如果上传响应时间 > 10秒，说明仍然是同步处理")
    print("- 监控处理进度可以确认向量化是否在后台正常进行")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main()) 