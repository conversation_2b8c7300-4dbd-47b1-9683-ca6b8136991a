"""
LLM切换使用示例
演示如何在DeepSeek和OpenAI之间切换
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.llm import (
    LLMFactory, 
    LLMConfig, 
    LLMProvider, 
    create_llm_from_settings,
    LLMError
)
from config import settings


def test_deepseek_llm():
    """测试DeepSeek LLM"""
    print("=" * 50)
    print("测试 DeepSeek LLM")
    print("=" * 50)
    
    try:
        # 方法1：直接使用工厂创建
        config = LLMConfig(
            provider=LLMProvider.DEEPSEEK,
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            model="deepseek-reasoner",
            temperature=0.1,
            max_tokens=1000
        )
        
        llm = LLMFactory.create_llm(config)
        
        # 测试调用
        test_prompt = "请简单介绍一下人工智能的发展历程。"
        print(f"提问: {test_prompt}")
        
        response = llm.invoke(test_prompt)
        print(f"回答: {response.content[:200]}...")
        print(f"模型: {response.model}")
        print(f"使用情况: {response.usage}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek测试失败: {e}")
        return False


def test_openai_llm():
    """测试OpenAI LLM"""
    print("=" * 50)
    print("测试 OpenAI LLM")
    print("=" * 50)
    
    try:
        # 方法1：直接使用工厂创建
        config = LLMConfig(
            provider=LLMProvider.OPENAI,
            api_key=os.getenv("OPENAI_API_KEY"),
            model="gpt-3.5-turbo",
            temperature=0.1,
            max_tokens=1000
        )
        
        llm = LLMFactory.create_llm(config)
        
        # 测试调用
        test_prompt = "请简单介绍一下机器学习的基本概念。"
        print(f"提问: {test_prompt}")
        
        response = llm.invoke(test_prompt)
        print(f"回答: {response.content[:200]}...")
        print(f"模型: {response.model}")
        print(f"使用情况: {response.usage}")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAI测试失败: {e}")
        return False


def test_config_switching():
    """测试通过配置切换LLM"""
    print("=" * 50)
    print("测试配置切换")
    print("=" * 50)
    
    # 测试DeepSeek配置
    print("1. 使用DeepSeek配置")
    try:
        # 临时修改配置
        original_provider = settings.LLM_PROVIDER
        settings.LLM_PROVIDER = "deepseek"
        
        llm = create_llm_from_settings(settings, langchain_compatible=False)
        
        test_prompt = "什么是深度学习？"
        print(f"提问: {test_prompt}")
        
        response = llm.invoke(test_prompt)
        print(f"回答: {response.content[:150]}...")
        print(f"提供商: {llm.get_provider().value}")
        
        # 恢复原始配置
        settings.LLM_PROVIDER = original_provider
        
    except Exception as e:
        print(f"❌ DeepSeek配置测试失败: {e}")
    
    # 测试OpenAI配置
    print("\n2. 使用OpenAI配置")
    try:
        # 临时修改配置
        original_provider = settings.LLM_PROVIDER
        settings.LLM_PROVIDER = "openai"
        
        llm = create_llm_from_settings(settings, langchain_compatible=False)
        
        test_prompt = "什么是自然语言处理？"
        print(f"提问: {test_prompt}")
        
        response = llm.invoke(test_prompt)
        print(f"回答: {response.content[:150]}...")
        print(f"提供商: {llm.get_provider().value}")
        
        # 恢复原始配置
        settings.LLM_PROVIDER = original_provider
        
    except Exception as e:
        print(f"❌ OpenAI配置测试失败: {e}")


def test_langchain_compatibility():
    """测试LangChain兼容性"""
    print("=" * 50)
    print("测试 LangChain 兼容性")
    print("=" * 50)
    
    try:
        # 创建LangChain兼容的LLM
        llm = create_llm_from_settings(settings, langchain_compatible=True)
        
        test_prompt = "请解释什么是RAG（检索增强生成）？"
        print(f"提问: {test_prompt}")
        
        # 使用LangChain接口调用
        response = llm.invoke(test_prompt)
        print(f"回答: {response.content[:200]}...")
        print(f"LLM类型: {llm._llm_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ LangChain兼容性测试失败: {e}")
        return False


def show_available_providers():
    """显示可用的提供商"""
    print("=" * 50)
    print("可用的LLM提供商")
    print("=" * 50)
    
    providers = LLMFactory.get_available_providers()
    for provider in providers:
        print(f"- {provider.value}")


def main():
    """主函数"""
    print("🚀 LLM切换功能测试")
    print()
    
    # 显示可用提供商
    show_available_providers()
    print()
    
    # 检查API密钥
    deepseek_key = os.getenv("DEEPSEEK_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    print("API密钥状态:")
    print(f"- DeepSeek: {'✅ 已设置' if deepseek_key else '❌ 未设置'}")
    print(f"- OpenAI: {'✅ 已设置' if openai_key else '❌ 未设置'}")
    print()
    
    # 运行测试
    tests_passed = 0
    total_tests = 0
    
    if deepseek_key:
        total_tests += 1
        if test_deepseek_llm():
            tests_passed += 1
        print()
    
    if openai_key:
        total_tests += 1
        if test_openai_llm():
            tests_passed += 1
        print()
    
    # 配置切换测试（需要至少一个API密钥）
    if deepseek_key or openai_key:
        test_config_switching()
        print()
    
    # LangChain兼容性测试
    total_tests += 1
    if test_langchain_compatibility():
        tests_passed += 1
    
    # 总结
    print("=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"通过测试: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查API密钥和网络连接")


if __name__ == "__main__":
    main()
