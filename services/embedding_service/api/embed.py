"""
嵌入计算API
"""

import sys
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
from shared.utils.logger import get_logger
from services.embedding_service.config import config

logger = get_logger(__name__)

router = APIRouter(prefix="/embed", tags=["嵌入计算"])


# 请求模型
class EmbedDocumentsRequest(BaseModel):
    """批量文档嵌入请求"""
    texts: List[str] = Field(..., description="要嵌入的文本列表", min_items=1, max_items=100)
    normalize: bool = Field(True, description="是否标准化嵌入向量")
    
    class Config:
        schema_extra = {
            "example": {
                "texts": ["这是第一个文档", "这是第二个文档"],
                "normalize": True
            }
        }


class EmbedQueryRequest(BaseModel):
    """单个查询嵌入请求"""
    text: str = Field(..., description="要嵌入的查询文本", min_length=1, max_length=8192)
    normalize: bool = Field(True, description="是否标准化嵌入向量")
    
    class Config:
        schema_extra = {
            "example": {
                "text": "这是一个查询文本",
                "normalize": True
            }
        }


# 响应模型
class EmbedResponse(BaseModel):
    """嵌入响应"""
    embeddings: List[List[float]] = Field(..., description="嵌入向量列表")
    dimension: int = Field(..., description="嵌入向量维度")
    count: int = Field(..., description="嵌入向量数量")
    processing_time: float = Field(..., description="处理时间（秒）")
    timestamp: str = Field(..., description="处理时间戳")


class ModelInfoResponse(BaseModel):
    """模型信息响应"""
    model_name: str = Field(..., description="模型名称")
    dimension: int = Field(..., description="嵌入维度")
    max_length: int = Field(..., description="最大序列长度")
    device: str = Field(..., description="运行设备")
    status: str = Field(..., description="模型状态")
    memory_usage: Dict[str, Any] = Field(..., description="内存使用情况")


def get_embedding_manager(request: Request):
    """获取嵌入管理器依赖"""
    if not hasattr(request.app.state, 'embedding_manager') or not request.app.state.embedding_manager:
        raise HTTPException(status_code=503, detail="嵌入服务未初始化")
    return request.app.state.embedding_manager


@router.post("/documents", response_model=EmbedResponse)
async def embed_documents(
    request: EmbedDocumentsRequest,
    embedding_manager=Depends(get_embedding_manager)
) -> EmbedResponse:
    """
    批量文档嵌入
    
    Args:
        request: 嵌入请求
        embedding_manager: 嵌入管理器
        
    Returns:
        嵌入结果
    """
    try:
        start_time = datetime.utcnow()
        
        logger.info(f"开始批量文档嵌入，文档数量: {len(request.texts)}")
        
        # 执行嵌入计算
        embeddings = await embedding_manager.embed_documents(
            texts=request.texts,
            normalize=request.normalize
        )
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        logger.info(f"批量文档嵌入完成，耗时: {processing_time:.3f}秒")
        
        return EmbedResponse(
            embeddings=embeddings,
            dimension=len(embeddings[0]) if embeddings else 0,
            count=len(embeddings),
            processing_time=processing_time,
            timestamp=end_time.isoformat()
        )
        
    except Exception as e:
        logger.error(f"批量文档嵌入失败: {e}")
        raise HTTPException(status_code=500, detail=f"嵌入计算失败: {str(e)}")


@router.post("/query", response_model=EmbedResponse)
async def embed_query(
    request: EmbedQueryRequest,
    embedding_manager=Depends(get_embedding_manager)
) -> EmbedResponse:
    """
    单个查询嵌入
    
    Args:
        request: 嵌入请求
        embedding_manager: 嵌入管理器
        
    Returns:
        嵌入结果
    """
    try:
        start_time = datetime.utcnow()
        
        logger.debug(f"开始查询嵌入，文本长度: {len(request.text)}")
        
        # 执行嵌入计算
        embedding = await embedding_manager.embed_query(
            text=request.text,
            normalize=request.normalize
        )
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        logger.debug(f"查询嵌入完成，耗时: {processing_time:.3f}秒")
        
        return EmbedResponse(
            embeddings=[embedding],
            dimension=len(embedding),
            count=1,
            processing_time=processing_time,
            timestamp=end_time.isoformat()
        )
        
    except Exception as e:
        logger.error(f"查询嵌入失败: {e}")
        raise HTTPException(status_code=500, detail=f"嵌入计算失败: {str(e)}")


@router.get("/model/info", response_model=ModelInfoResponse)
async def get_model_info(
    embedding_manager=Depends(get_embedding_manager)
) -> ModelInfoResponse:
    """
    获取模型信息
    
    Args:
        embedding_manager: 嵌入管理器
        
    Returns:
        模型信息
    """
    try:
        model_info = await embedding_manager.get_model_info()
        
        return ModelInfoResponse(
            model_name=model_info["model_name"],
            dimension=model_info["dimension"],
            max_length=model_info["max_length"],
            device=model_info["device"],
            status=model_info["status"],
            memory_usage=model_info.get("memory_usage", {})
        )
        
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型信息失败: {str(e)}")


@router.post("/model/reload")
async def reload_model(
    embedding_manager=Depends(get_embedding_manager)
) -> Dict[str, Any]:
    """
    重新加载模型
    
    Args:
        embedding_manager: 嵌入管理器
        
    Returns:
        重载结果
    """
    try:
        logger.info("开始重新加载模型...")
        
        await embedding_manager.reload_model()
        
        logger.info("模型重新加载完成")
        
        return {
            "status": "success",
            "message": "模型重新加载完成",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"模型重新加载失败: {e}")
        raise HTTPException(status_code=500, detail=f"模型重新加载失败: {str(e)}")


@router.post("/cache/clear")
async def clear_cache(
    embedding_manager=Depends(get_embedding_manager)
) -> Dict[str, Any]:
    """
    清理缓存
    
    Args:
        embedding_manager: 嵌入管理器
        
    Returns:
        清理结果
    """
    try:
        logger.info("开始清理缓存...")
        
        await embedding_manager.clear_cache()
        
        logger.info("缓存清理完成")
        
        return {
            "status": "success",
            "message": "缓存清理完成",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"缓存清理失败: {e}")
        raise HTTPException(status_code=500, detail=f"缓存清理失败: {str(e)}")
