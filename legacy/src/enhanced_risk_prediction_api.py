"""
增强版风险预测API
支持多种分析策略：智能选择策略和多批次分析策略
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from enum import Enum

from .risk_prediction_api import RiskPredictionAPI
from .batch_risk_analyzer import BatchRiskAnalyzer
from .file_coded_vector_store import FileCodedVectorStore
from .excel_risk_processor import ExcelRiskProcessor

logger = logging.getLogger(__name__)


class AnalysisStrategy(Enum):
    """分析策略枚举"""

    SMART_SELECTION = "smart_selection"  # 智能选择策略（默认）
    MULTI_BATCH = "multi_batch"  # 多批次分析策略


class EnhancedRiskPredictionAPI:
    """
    增强版风险预测API

    提供多种分析策略：
    1. 智能选择策略：基于重要性评分选择最相关的文档
    2. 多批次分析策略：将所有文档分批处理，确保全覆盖
    """

    def __init__(self):
        """初始化增强版风险预测API"""
        # 初始化基础组件
        self.file_coded_vector_store = FileCodedVectorStore()
        self.excel_processor = ExcelRiskProcessor()

        # 初始化基础API（智能选择策略）
        self.base_api = RiskPredictionAPI()

        # 初始化多批次分析器
        self.batch_analyzer = BatchRiskAnalyzer(
            file_coded_vector_store=self.file_coded_vector_store
        )

        logger.info("✅ 增强版风险预测API初始化完成")
        logger.info("支持的分析策略: 智能选择、多批次分析")

    def predict_by_file_code(
        self,
        file_code: str,
        strategy: AnalysisStrategy = AnalysisStrategy.SMART_SELECTION,
    ) -> Dict[str, Any]:
        """
        使用指定策略进行风险预测

        Args:
            file_code: 文件编码
            strategy: 分析策略

        Returns:
            风险预测结果
        """
        try:
            logger.info(f"开始风险预测，文件编码: {file_code}, 策略: {strategy.value}")

            if strategy == AnalysisStrategy.SMART_SELECTION:
                return self._predict_with_smart_selection(file_code)
            elif strategy == AnalysisStrategy.MULTI_BATCH:
                return self._predict_with_multi_batch(file_code)
            else:
                return {
                    "success": False,
                    "error": f"不支持的分析策略: {strategy.value}",
                }

        except Exception as e:
            logger.error(f"风险预测失败: {e}")
            return {"success": False, "error": f"风险预测失败: {str(e)}"}

    def _predict_with_smart_selection(self, file_code: str) -> Dict[str, Any]:
        """
        使用智能选择策略进行预测

        Args:
            file_code: 文件编码

        Returns:
            预测结果
        """
        logger.info("使用智能选择策略进行风险预测")
        result = self.base_api.predict_by_file_code(file_code)

        if result.get("success"):
            result["analysis_strategy"] = "smart_selection"
            result["strategy_description"] = (
                "基于重要性评分智能选择最相关的文档进行分析"
            )

        return result

    def _predict_with_multi_batch(self, file_code: str) -> Dict[str, Any]:
        """
        使用多批次分析策略进行预测

        Args:
            file_code: 文件编码

        Returns:
            预测结果
        """
        logger.info("使用多批次分析策略进行风险预测")
        result = self.batch_analyzer.generate_risk_report_by_file_code(file_code)

        if result.get("success"):
            result["strategy_description"] = "将所有文档分批处理，确保全面覆盖所有数据"

        return result

    def compare_strategies(self, file_code: str) -> Dict[str, Any]:
        """
        比较两种策略的分析结果

        Args:
            file_code: 文件编码

        Returns:
            比较结果
        """
        try:
            logger.info(f"开始策略比较分析，文件编码: {file_code}")

            # 使用智能选择策略
            smart_result = self._predict_with_smart_selection(file_code)

            # 使用多批次策略
            batch_result = self._predict_with_multi_batch(file_code)

            # 生成比较报告
            comparison = self._generate_comparison_report(smart_result, batch_result)

            return {
                "success": True,
                "file_code": file_code,
                "smart_selection_result": smart_result,
                "multi_batch_result": batch_result,
                "comparison": comparison,
            }

        except Exception as e:
            logger.error(f"策略比较失败: {e}")
            return {"success": False, "error": f"策略比较失败: {str(e)}"}

    def _generate_comparison_report(
        self, smart_result: Dict[str, Any], batch_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        生成策略比较报告

        Args:
            smart_result: 智能选择策略结果
            batch_result: 多批次策略结果

        Returns:
            比较报告
        """
        comparison = {
            "strategy_comparison": {},
            "risk_assessment_comparison": {},
            "recommendations": [],
        }

        # 策略特点比较
        if smart_result.get("success") and batch_result.get("success"):
            comparison["strategy_comparison"] = {
                "smart_selection": {
                    "documents_used": smart_result.get("documents_analyzed", "unknown"),
                    "analysis_speed": "快速",
                    "data_coverage": "重点覆盖",
                    "suitable_for": "快速评估、重点分析",
                },
                "multi_batch": {
                    "documents_used": f"全部文档分{batch_result.get('total_batches', 'unknown')}批处理",
                    "analysis_speed": "较慢",
                    "data_coverage": "全面覆盖",
                    "suitable_for": "详细评估、全面分析",
                },
            }

            # 风险评估比较
            smart_prob = smart_result.get("default_probability", 0)
            batch_prob = batch_result.get("default_probability", 0)
            smart_level = smart_result.get("risk_level", "")
            batch_level = batch_result.get("risk_level", "")

            comparison["risk_assessment_comparison"] = {
                "default_probability": {
                    "smart_selection": smart_prob,
                    "multi_batch": batch_prob,
                    "difference": abs(smart_prob - batch_prob),
                    "consistency": (
                        "高"
                        if abs(smart_prob - batch_prob) < 0.1
                        else "中" if abs(smart_prob - batch_prob) < 0.2 else "低"
                    ),
                },
                "risk_level": {
                    "smart_selection": smart_level,
                    "multi_batch": batch_level,
                    "consistent": smart_level == batch_level,
                },
            }

            # 生成建议
            prob_diff = abs(smart_prob - batch_prob)
            if prob_diff < 0.05:
                comparison["recommendations"].append(
                    "两种策略结果高度一致，可使用智能选择策略进行快速评估"
                )
            elif prob_diff < 0.15:
                comparison["recommendations"].append(
                    "两种策略结果基本一致，建议根据时间要求选择策略"
                )
            else:
                comparison["recommendations"].append(
                    "两种策略结果存在较大差异，建议使用多批次策略进行详细分析"
                )

            if smart_level != batch_level:
                comparison["recommendations"].append(
                    "风险等级评估不一致，建议进一步核实数据或使用多批次策略"
                )

        return comparison

    # 继承基础API的其他方法
    def upload_excel_and_get_code(self, file_path: str) -> Dict[str, Any]:
        """上传Excel文件并获取文件编码"""
        return self.base_api.upload_excel_and_get_code(file_path)

    def list_available_files(self) -> List[Dict[str, Any]]:
        """列出可用的文件"""
        return self.base_api.list_available_files()

    def remove_file(self, file_code: str) -> Dict[str, Any]:
        """删除文件"""
        return self.base_api.remove_file(file_code)

    def get_file_info(self, file_code: str) -> Dict[str, Any]:
        """获取文件信息"""
        return self.base_api.get_file_info(file_code)


# 便捷函数
def create_enhanced_api() -> EnhancedRiskPredictionAPI:
    """创建增强版API实例"""
    return EnhancedRiskPredictionAPI()


def quick_predict(file_code: str, strategy: str = "smart") -> Dict[str, Any]:
    """
    快速预测函数

    Args:
        file_code: 文件编码
        strategy: 策略类型 ("smart" 或 "batch")

    Returns:
        预测结果
    """
    api = create_enhanced_api()

    if strategy.lower() in ["smart", "smart_selection"]:
        return api.predict_by_file_code(file_code, AnalysisStrategy.SMART_SELECTION)
    elif strategy.lower() in ["batch", "multi_batch"]:
        return api.predict_by_file_code(file_code, AnalysisStrategy.MULTI_BATCH)
    else:
        return {
            "success": False,
            "error": f"不支持的策略: {strategy}，请使用 'smart' 或 'batch'",
        }


def compare_all_strategies(file_code: str) -> Dict[str, Any]:
    """
    比较所有策略的结果

    Args:
        file_code: 文件编码

    Returns:
        比较结果
    """
    api = create_enhanced_api()
    return api.compare_strategies(file_code)
