#!/bin/bash

# 向量服务启动脚本
# 包含PostHog和LangChain遥测禁用设置

echo "🚀 启动向量服务..."

# 完全禁用所有遥测数据发送，保护隐私和数据安全
export POSTHOG_HOST=""
export POSTHOG_PROJECT_ID=""
export POSTHOG_API_KEY=""
export POSTHOG_FEATURE_FLAGS="false"

# 禁用LangChain遥测和分析
export LANGCHAIN_TRACING_V2="false"
export LANGCHAIN_ANALYTICS="false"
export LANGCHAIN_TRACING="false"
export LANGCHAIN_TRACKING="false"
export LANGCHAIN_ENDPOINT=""
export LANGCHAIN_API_KEY=""

# 禁用其他可能的遥测
export TELEMETRY_DISABLED="true"
export DO_NOT_TRACK="1"
export ANALYTICS_DISABLED="true"

echo "✅ 遥测数据发送已禁用"

# 启动向量服务
echo "📡 启动向量服务..."
export PYTHONPATH="/Users/<USER>/anaconda3/envs/langchain_env/bin/python:$(pwd)"
python services/vector_service/main.py 