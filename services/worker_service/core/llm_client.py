"""
LLM客户端 - Worker服务
基于shared/llm中的统一LLM工厂模式，用于Worker中的非流式分析
"""

from shared.llm import create_llm_from_settings, get_factory_info
from shared.utils.logger import get_logger
from services.worker_service.config import config

logger = get_logger(__name__)


class LLMClient:
    """LLM客户端 - 基于shared/llm统一工厂模式"""

    def __init__(self):
        self.llm = None
        self._initialize_llm()

        logger.info(f"LLM客户端初始化完成: 提供商={config.llm_provider}")

    def _initialize_llm(self):
        """初始化LLM实例"""
        try:
            # 使用shared/llm工厂模式创建LLM实例
            self.llm = create_llm_from_settings(
                settings=config,
                instance_key="worker_llm",
                langchain_compatible=False  # Worker中不需要LangChain兼容性
            )

            logger.info(f"LLM实例创建成功: {self.llm.get_provider().value} - {self.llm.get_model()}")
            
            # 输出工厂信息用于调试
            factory_info = get_factory_info()
            logger.debug(f"LLM工厂信息: {factory_info}")

        except Exception as e:
            logger.error(f"LLM实例创建失败: {e}")
            logger.error(f"配置信息: 提供商={getattr(config, 'llm_provider', 'unknown')}, "
                        f"API密钥={'已设置' if getattr(config, 'deepseek_api_key', None) else '未设置'}")
            raise

    async def analyze(
        self,
        prompt: str,
        analysis_type: str = "default",
        streaming: bool = False
    ) -> str:
        """
        执行LLM分析

        Args:
            prompt: 分析提示词
            analysis_type: 分析类型
            streaming: 是否流式输出 (Worker中固定为False)

        Returns:
            分析结果文本
        """
        try:
            if not self.llm:
                raise ValueError("LLM实例未初始化")

            # 根据分析类型调整参数
            kwargs = self._get_analysis_params(analysis_type)

            logger.debug(f"开始LLM分析: 类型={analysis_type}, 提供商={self.llm.get_provider().value}")

            # 调用LLM
            response = self.llm.invoke(prompt, **kwargs)

            logger.debug(f"LLM分析完成，响应长度: {len(response.content)}")
            return response.content

        except Exception as e:
            logger.error(f"LLM分析失败: {e}")
            raise

    def _get_analysis_params(self, analysis_type: str) -> dict:
        """根据分析类型获取参数"""
        params = {}

        if analysis_type == "quick":
            params.update({
                "max_tokens": 1000,
                "temperature": 0.1
            })
        elif analysis_type == "detailed":
            params.update({
                "max_tokens": 4000,
                "temperature": 0.2
            })
        else:  # default
            params.update({
                "max_tokens": 2000,
                "temperature": 0.1
            })

        return params

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            logger.info("开始LLM客户端健康检查...")
            
            if not self.llm:
                logger.error("LLM实例未初始化")
                return False
            
            logger.info(f"LLM实例存在: {self.llm.get_provider().value} - {self.llm.get_model()}")
            
            # 使用shared/llm的健康检查方法
            logger.info("执行LLM基础健康检查...")
            result = self.llm.health_check()
            
            if result:
                logger.info("✅ LLM健康检查通过")
            else:
                logger.warning("❌ LLM健康检查失败")
            
            return result

        except Exception as e:
            logger.error(f"LLM健康检查异常: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

    async def cleanup(self):
        """清理资源"""
        # shared/llm工厂模式的实例会自动管理资源
        logger.debug("LLM客户端清理完成")
