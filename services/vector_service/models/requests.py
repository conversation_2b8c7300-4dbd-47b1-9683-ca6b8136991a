"""
向量化服务请求模型
"""

from typing import Optional
from pydantic import BaseModel
from fastapi import UploadFile


class UploadRequest(BaseModel):
    """文件上传请求"""
    force_reprocess: bool = False
    chunk_size: Optional[int] = None
    chunk_overlap: Optional[int] = None


class VectorizeRequest(BaseModel):
    """向量化请求"""
    file_code: str
    force_reprocess: bool = False
    chunk_size: Optional[int] = None
    chunk_overlap: Optional[int] = None


class StatusRequest(BaseModel):
    """状态查询请求"""
    file_code: str
