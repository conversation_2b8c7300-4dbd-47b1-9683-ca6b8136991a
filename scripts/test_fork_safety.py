#!/usr/bin/env python3
"""
测试 OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES 环境变量
验证是否能解决 macOS MPS+fork 冲突问题
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_with_env_var():
    """测试使用环境变量的方案"""
    print("🧪 测试方案 1: OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES")
    print("=" * 60)
    
    # 设置环境变量
    env = os.environ.copy()
    env['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'
    
    # 创建测试脚本
    test_script = """
import sys
sys.path.insert(0, '.')
from services.worker_service.tasks.health import health_check

print('开始测试健康检查任务（使用环境变量）...')
try:
    # 强制使用进程池模式进行测试
    import os
    os.environ['FORCE_PROCESS_POOL'] = '1'
    
    result = health_check.apply()
    task_result = result.get(timeout=30)
    print('✅ 健康检查任务执行成功!')
    print(f'整体健康状态: {task_result.get("overall_healthy", False)}')
    print(f'检查耗时: {task_result.get("check_time", 0):.2f}秒')
except Exception as e:
    print(f'❌ 健康检查任务执行失败: {e}')
    sys.exit(1)
"""
    
    try:
        # 运行测试
        result = subprocess.run(
            [sys.executable, '-c', test_script],
            env=env,
            capture_output=True,
            text=True,
            timeout=60,
            cwd=str(project_root)
        )
        
        print("📤 输出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 环境变量方案测试成功!")
            return True
        else:
            print(f"❌ 环境变量方案测试失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        return False


def test_current_solution():
    """测试当前的线程池方案"""
    print("\n🧪 测试方案 2: 线程池方案（当前实现）")
    print("=" * 60)
    
    test_script = """
import sys
sys.path.insert(0, '.')
from services.worker_service.tasks.health import health_check

print('开始测试健康检查任务（线程池方案）...')
try:
    result = health_check.apply()
    task_result = result.get(timeout=30)
    print('✅ 健康检查任务执行成功!')
    print(f'整体健康状态: {task_result.get("overall_healthy", False)}')
    print(f'检查耗时: {task_result.get("check_time", 0):.2f}秒')
except Exception as e:
    print(f'❌ 健康检查任务执行失败: {e}')
    sys.exit(1)
"""
    
    try:
        result = subprocess.run(
            [sys.executable, '-c', test_script],
            capture_output=True,
            text=True,
            timeout=60,
            cwd=str(project_root)
        )
        
        print("📤 输出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 线程池方案测试成功!")
            return True
        else:
            print(f"❌ 线程池方案测试失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        return False


def test_celery_worker_with_env():
    """测试 Celery Worker 使用环境变量启动"""
    print("\n🧪 测试方案 3: Celery Worker + 环境变量")
    print("=" * 60)
    
    # 设置环境变量
    env = os.environ.copy()
    env['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'
    
    try:
        # 启动 Celery Worker（进程池模式）
        cmd = [
            'celery', '-A', 'services.worker_service.worker', 'worker',
            '--pool', 'processes',
            '--concurrency', '1',
            '--loglevel', 'info',
            '--max-tasks-per-child', '1',  # 只处理一个任务就退出
        ]
        
        print(f"🚀 启动命令: {' '.join(cmd)}")
        print("⏳ 启动 Worker（10秒后自动停止）...")
        
        process = subprocess.Popen(
            cmd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=str(project_root)
        )
        
        # 等待一段时间让 Worker 启动
        time.sleep(10)
        
        # 终止进程
        process.terminate()
        stdout, stderr = process.communicate(timeout=5)
        
        print("📤 Worker 输出:")
        print(stdout[-1000:] if len(stdout) > 1000 else stdout)  # 只显示最后1000字符
        
        if stderr:
            print("⚠️  Worker 错误输出:")
            print(stderr[-1000:] if len(stderr) > 1000 else stderr)
        
        # 检查是否有崩溃信息
        if 'SIGABRT' in stderr or 'MPSGraphObject' in stderr:
            print("❌ 仍然存在 MPS+fork 冲突")
            return False
        else:
            print("✅ 未检测到 MPS+fork 冲突")
            return True
            
    except subprocess.TimeoutExpired:
        print("⏰ Worker 测试超时")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ Worker 测试异常: {e}")
        return False


def main():
    """主函数"""
    print("🔬 macOS fork 安全性测试")
    print("测试 OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES 环境变量的效果")
    print("=" * 80)
    
    results = []
    
    # 测试环境变量方案
    env_result = test_with_env_var()
    results.append(("环境变量方案", env_result))
    
    # 测试当前线程池方案
    thread_result = test_current_solution()
    results.append(("线程池方案", thread_result))
    
    # 测试 Celery Worker
    worker_result = test_celery_worker_with_env()
    results.append(("Celery Worker + 环境变量", worker_result))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{name}: {status}")
    
    print("\n💡 建议:")
    if env_result and worker_result:
        print("🎯 环境变量方案可行！可以考虑作为更简单的解决方案。")
        print("   优点: 保持进程池架构，配置简单")
        print("   缺点: 绕过系统安全检查，可能存在潜在风险")
    elif thread_result:
        print("🛡️  线程池方案更安全可靠，建议继续使用当前实现。")
        print("   优点: 彻底解决问题，架构清晰")
        print("   缺点: 改变了 Worker 池类型")
    else:
        print("⚠️  需要进一步调试和分析问题。")
    
    print("\n🔧 如果选择环境变量方案，可以这样配置:")
    print("export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES")
    print("celery -A services.worker_service.worker worker --pool processes")


if __name__ == "__main__":
    main()
