"""
风险分析器 - 基于原有risk_analyzer.py实现
"""

import os
import json
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from shared.utils.logger import get_logger
from shared.models.analysis_result import AnalysisResult, AnalysisStatus, RiskLevel
from services.analysis_service.config import config
from services.analysis_service.core.chroma_retriever import ChromaRetriever
from services.analysis_service.llm import create_llm_from_settings, LLMError
from services.analysis_service.risk_config import RiskAnalysisConfig

logger = get_logger(__name__)


class RiskAnalyzer:
    """风险分析器 - 基于原有实现"""

    def __init__(
        self,
        chroma_retriever: Optional[ChromaRetriever] = None,
        api_key: Optional[str] = None,
        model_name: str = None,
        temperature: float = None,
        max_tokens: int = None,
    ):
        """
        初始化风险分析器

        Args:
            chroma_retriever: ChromaDB检索器
            api_key: API密钥
            model_name: 模型名称
            temperature: 生成温度
            max_tokens: 最大生成长度
        """
        # 初始化检索器
        self.chroma_retriever = chroma_retriever or ChromaRetriever()

        # 配置 LLM（保持向后兼容）
        self.api_key = api_key or config.deepseek_api_key
        self.model_name = model_name or config.deepseek_model
        self.temperature = temperature or config.temperature
        self.max_tokens = max_tokens or config.max_tokens

        # 初始化 LLM（使用原有的工厂模式）
        try:
            # 创建临时配置对象
            llm_config = type(
                "Config",
                (),
                {
                    "LLM_PROVIDER": config.llm_provider,
                    "DEEPSEEK_API_KEY": self.api_key,
                    "DEEPSEEK_MODEL": self.model_name,
                    "OPENAI_API_KEY": config.openai_api_key,
                    "OPENAI_MODEL": config.openai_model,
                    "TEMPERATURE": self.temperature,
                    "MAX_TOKENS": self.max_tokens,
                },
            )()

            self.llm = create_llm_from_settings(
                llm_config, instance_key="risk_analyzer", langchain_compatible=False
            )
            logger.info(f"✅ 使用LLM提供商: {config.llm_provider}")
        except LLMError as e:
            logger.error(f"LLM初始化失败: {e}")
            raise ValueError(f"LLM初始化失败: {e}") from e

        # 风控配置
        self.risk_config = RiskAnalysisConfig()

        # 流式输出配置
        self.enable_streaming = config.enable_streaming

        # 结果存储
        self.results_dir = Path(config.results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)

        logger.info("✅ 风险分析器初始化完成")
        logger.info(f"模型: {self.model_name}")
        logger.info(f"温度: {self.temperature}")
        logger.info(f"流式输出: {'启用' if self.enable_streaming else '禁用'}")

    def analyze_risk(
        self,
        file_code: str,
        analysis_type: str = "default",
        options: Optional[Dict[str, Any]] = None,
    ) -> AnalysisResult:
        """
        执行风险分析

        Args:
            file_code: 文件编码
            analysis_type: 分析类型
            options: 分析选项

        Returns:
            分析结果
        """
        analysis_id = str(uuid.uuid4())

        # 创建初始分析结果
        result = AnalysisResult(
            analysis_id=analysis_id,
            file_code=file_code,
            status=AnalysisStatus.PROCESSING,
            created_at=datetime.now(),
        )

        try:
            logger.info(f"开始风险分析: {file_code} (analysis_id: {analysis_id})")

            # 1. 检索相关文档
            documents = self._retrieve_risk_documents(file_code)
            if not documents:
                result.status = AnalysisStatus.FAILED
                result.error_message = f"未找到文件编码 {file_code} 对应的风控数据"
                return result

            result.document_count = len(documents)

            # 2. 提取风控数据
            risk_data = self._extract_risk_data_from_documents(documents)

            # 3. 构建分析提示词
            prompt = self._build_analysis_prompt(risk_data, file_code)

            # 4. 执行LLM分析
            start_time = datetime.now()
            if self.enable_streaming:
                analysis_content = self._stream_analysis(prompt)
            else:
                analysis_content = self._non_stream_analysis(prompt)

            processing_time = (datetime.now() - start_time).total_seconds()

            # 5. 解析分析结果
            parsed_result = self._parse_analysis_result(analysis_content)

            # 6. 更新结果
            result.status = AnalysisStatus.COMPLETED
            result.default_probability = parsed_result.get("default_probability")
            result.risk_level = parsed_result.get("risk_level")
            result.risk_score = parsed_result.get("risk_score")
            result.analysis_summary = parsed_result.get(
                "analysis_summary", analysis_content
            )
            result.key_factors = parsed_result.get("key_factors", [])
            result.recommendations = parsed_result.get("recommendations", [])
            result.confidence_score = parsed_result.get("confidence_score")
            result.processing_time = processing_time
            result.completed_at = datetime.now()

            # 7. 保存结果
            self._save_analysis_result(result)

            logger.info(
                f"✅ 风险分析完成: {file_code} (违约概率: {result.default_probability:.2%})"
            )
            return result

        except Exception as e:
            logger.error(f"风险分析失败: {e}")
            result.status = AnalysisStatus.FAILED
            result.error_message = str(e)
            result.completed_at = datetime.now()
            return result

    def _retrieve_risk_documents(self, file_code: str) -> List[Dict[str, Any]]:
        """
        检索风控相关文档

        Args:
            file_code: 文件编码

        Returns:
            相关文档列表
        """
        # 使用通用查询来获取所有相关文档
        query = "风控数据 用户信息 特征 风险"

        documents = self.chroma_retriever.retrieve_documents(
            file_code=file_code, query=query, top_k=20  # 获取更多文档以确保完整性
        )

        logger.info(f"检索到 {len(documents)} 个相关文档")
        return documents

    def _extract_risk_data_from_documents(self, documents: List[Dict[str, Any]]) -> str:
        """
        从文档中提取风控数据 - 基于原有逻辑

        Args:
            documents: 文档列表

        Returns:
            格式化的风控数据字符串
        """
        if not documents:
            return "未找到相关风控数据"

        # 增加总长度限制，充分利用DeepSeek的上下文能力
        max_total_length = 160000  # 约40K tokens，为prompt和response留足空间

        # 智能文档选择策略
        selected_docs = self._select_important_documents(documents, max_total_length)

        risk_data_parts = []
        current_length = 0

        for i, doc in enumerate(selected_docs, 1):
            # 获取文档元数据
            metadata = doc.get("metadata", {})
            file_name = metadata.get("file_name", "未知文件")
            chunk_id = metadata.get("chunk_id", "")

            content = doc.get("content", "").strip()

            # 格式化文档内容，包含更多元数据信息
            chunk_info = f" (块 {chunk_id})" if chunk_id != "" else ""

            data_part = f"""
数据源 {i}{chunk_info}:
文件: {file_name}
内容: {content}
---"""

            risk_data_parts.append(data_part)
            current_length += len(data_part)

        result = "\n".join(risk_data_parts)
        logger.info(
            f"提取风控数据完成，总长度: {len(result)} 字符，文档数: {len(risk_data_parts)}/{len(documents)}"
        )
        return result

    def _select_important_documents(
        self, documents: List[Dict[str, Any]], max_length: int
    ) -> List[Dict[str, Any]]:
        """
        智能选择重要文档，最大化数据利用率 - 基于原有逻辑

        Args:
            documents: 所有文档
            max_length: 最大总长度

        Returns:
            选择的重要文档列表
        """
        # 策略1: 优先选择包含关键风控指标的文档
        risk_keywords = [
            "收入",
            "负债",
            "信用",
            "逾期",
            "还款",
            "征信",
            "资产",
            "流水",
            "工资",
            "房贷",
            "车贷",
            "信用卡",
            "借款",
            "担保",
            "抵押",
            "年龄",
            "学历",
            "职业",
            "工作",
            "婚姻",
            "户籍",
            "风险",
            "压力",
            "评分",
            "等级",
            "历史",
            "记录",
            "违约",
            "欠款",
        ]

        # 为文档计算重要性分数
        scored_docs = []
        for doc in documents:
            content = doc.get("content", "")
            score = 0

            # 关键词匹配分数
            for keyword in risk_keywords:
                score += content.count(keyword) * 2

            # 相似度分数
            similarity = doc.get("similarity", 0)
            score += similarity * 10

            # 内容长度分数（适中长度更有价值）
            content_length = len(content)
            if 500 <= content_length <= 3000:
                score += 5
            elif content_length > 3000:
                score += 2

            scored_docs.append((score, doc))

        # 按分数排序
        scored_docs.sort(key=lambda x: x[0], reverse=True)

        # 策略2: 贪心选择，在长度限制内选择最多高分文档
        selected_docs = []
        current_length = 0

        for score, doc in scored_docs:
            content = doc.get("content", "")
            doc_length = len(content) + 200  # 加上格式化开销

            if current_length + doc_length <= max_length:
                selected_docs.append(doc)
                current_length += doc_length
            else:
                # 如果当前文档太长，尝试截断
                remaining_space = max_length - current_length - 200
                if remaining_space > 500:  # 至少保留500字符才有意义
                    truncated_doc = doc.copy()
                    truncated_doc["content"] = content[:remaining_space] + "...[截断]"
                    selected_docs.append(truncated_doc)
                break

        logger.info(
            f"智能选择文档: {len(selected_docs)}/{len(documents)} 个文档，总长度: {current_length}"
        )
        return selected_docs

    def _build_analysis_prompt(self, risk_data: str, file_code: str) -> str:
        """
        构建分析提示词

        Args:
            risk_data: 风控数据
            file_code: 文件编码

        Returns:
            分析提示词
        """
        # 加载提示词模板
        prompt_template = self._load_prompt_template()

        # 替换变量
        prompt = prompt_template.format(
            risk_data=risk_data,
            file_code=file_code,
            analysis_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        )

        return prompt

    def _load_prompt_template(self) -> str:
        """
        加载提示词模板

        Returns:
            提示词模板
        """
        try:
            prompt_file = Path(config.prompts_dir) / config.risk_analysis_prompt_file
            if prompt_file.exists():
                with open(prompt_file, "r", encoding="utf-8") as f:
                    return f.read()
            else:
                logger.warning(f"提示词文件不存在: {prompt_file}，使用默认模板")
                return self._get_default_prompt_template()
        except Exception as e:
            logger.error(f"加载提示词模板失败: {e}，使用默认模板")
            return self._get_default_prompt_template()

    def _get_default_prompt_template(self) -> str:
        """
        获取默认提示词模板

        Returns:
            默认提示词模板
        """
        return """你是一个专业的风险分析师，请基于以下风控数据对用户进行信贷风险评估。

## 风控数据
{risk_data}

## 分析要求
请从以下维度进行全面分析：
1. 违约概率评估（0-1之间的数值）
2. 风险等级（low/medium/high/very_high）
3. 关键风险因素识别
4. 风险缓解建议

## 输出格式
请以JSON格式输出分析结果：
```json
{{
    "default_probability": 0.15,
    "risk_level": "medium",
    "risk_score": 0.65,
    "analysis_summary": "详细的分析总结...",
    "key_factors": ["因素1", "因素2", "因素3"],
    "recommendations": ["建议1", "建议2", "建议3"],
    "confidence_score": 0.85
}}
```

文件编码: {file_code}
分析时间: {analysis_time}
"""

    def _stream_analysis(self, prompt: str) -> str:
        """
        执行流式分析 - 基于原有逻辑

        Args:
            prompt: 分析提示词

        Returns:
            分析结果内容
        """
        try:
            logger.info("开始流式AI分析...")
            collected_content = []

            # 流式调用LLM
            for chunk in self.llm.stream_invoke(prompt):
                if chunk:
                    # 实时输出chunk（改善用户体验）
                    print(chunk, end="", flush=True)
                    collected_content.append(chunk)

            # 输出换行符结束流式输出
            print()

            # 返回完整内容
            full_content = "".join(collected_content)
            logger.info(f"流式分析完成，总长度: {len(full_content)}")
            return full_content

        except Exception as e:
            logger.error(f"流式分析失败: {e}")
            logger.info("回退到非流式调用...")
            # 回退到非流式调用
            response = self.llm.invoke(prompt)
            return response.content

    def _non_stream_analysis(self, prompt: str) -> str:
        """
        执行非流式分析

        Args:
            prompt: 分析提示词

        Returns:
            分析结果内容
        """
        logger.info("开始非流式AI分析...")
        response = self.llm.invoke(prompt)
        logger.info(f"非流式分析完成，长度: {len(response.content)}")
        return response.content

    def _parse_analysis_result(self, analysis_content: str) -> Dict[str, Any]:
        """
        解析分析结果

        Args:
            analysis_content: 分析内容

        Returns:
            解析后的结果字典
        """
        try:
            # 尝试提取JSON部分
            import re

            json_match = re.search(
                r"```json\s*(\{.*?\})\s*```", analysis_content, re.DOTALL
            )
            if json_match:
                json_str = json_match.group(1)
                result = json.loads(json_str)

                # 验证和转换数据类型
                if "risk_level" in result:
                    risk_level_map = {
                        "low": RiskLevel.LOW,
                        "medium": RiskLevel.MEDIUM,
                        "high": RiskLevel.HIGH,
                        "very_high": RiskLevel.VERY_HIGH,
                    }
                    result["risk_level"] = risk_level_map.get(
                        result["risk_level"], RiskLevel.MEDIUM
                    )

                return result
            else:
                logger.warning("未找到JSON格式的分析结果，返回原始内容")
                return {"analysis_summary": analysis_content}

        except Exception as e:
            logger.error(f"解析分析结果失败: {e}")
            return {"analysis_summary": analysis_content}

    def _save_analysis_result(self, result: AnalysisResult) -> bool:
        """
        保存分析结果

        Args:
            result: 分析结果

        Returns:
            是否保存成功
        """
        try:
            result_file = self.results_dir / f"{result.analysis_id}.json"

            with open(result_file, "w", encoding="utf-8") as f:
                json.dump(result.dict(), f, ensure_ascii=False, indent=2, default=str)

            logger.info(f"分析结果已保存: {result_file}")
            return True

        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            return False

    def get_analysis_result(self, analysis_id: str) -> Optional[AnalysisResult]:
        """
        获取分析结果

        Args:
            analysis_id: 分析ID

        Returns:
            分析结果
        """
        try:
            result_file = self.results_dir / f"{analysis_id}.json"

            if not result_file.exists():
                return None

            with open(result_file, "r", encoding="utf-8") as f:
                data = json.load(f)

            return AnalysisResult(**data)

        except Exception as e:
            logger.error(f"加载分析结果失败: {e}")
            return None

    def health_check(self) -> Dict[str, bool]:
        """
        健康检查

        Returns:
            健康状态
        """
        checks = {}

        # 检查ChromaDB连接
        checks["chroma_retriever"] = self.chroma_retriever.health_check()

        # 检查LLM
        try:
            test_response = self.llm.invoke("健康检查测试")
            checks["llm"] = bool(test_response and test_response.content)
        except Exception:
            checks["llm"] = False

        # 检查结果存储目录
        checks["results_dir"] = self.results_dir.exists() and self.results_dir.is_dir()

        return checks
