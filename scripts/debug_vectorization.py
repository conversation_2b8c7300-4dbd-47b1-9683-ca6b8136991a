#!/usr/bin/env python3
"""
向量化任务调试工具
专门用于诊断向量化任务卡在0%进度的问题
"""

import os
import sys
import time
import tempfile
import traceback
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.utils.logger import get_logger
from shared.celery_config import celery_app
from shared.models.file_info import FileInfo, FileStatus

# 重要：导入worker模块以确保任务注册
import services.worker_service.worker

logger = get_logger(__name__)


def create_test_file():
    """创建测试文件"""
    test_content = """客户ID,年龄,收入,信用评分,负债比率,违约历史
001,35,50000,720,0.3,0
002,28,35000,650,0.5,1
003,42,75000,800,0.2,0
004,31,40000,600,0.6,1
005,38,60000,750,0.25,0
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        return f.name


def test_vectorization_task():
    """测试向量化任务"""
    print("🔍 开始向量化任务调试...")
    
    # 1. 创建测试文件
    print("1. 创建测试文件...")
    test_file_path = create_test_file()
    file_code = f"debug_test_{int(time.time())}"
    
    print(f"   测试文件: {test_file_path}")
    print(f"   文件编码: {file_code}")
    
    try:
        # 2. 创建FileInfo对象
        print("2. 创建FileInfo对象...")
        file_info = FileInfo(
            file_code=file_code,
            file_name="debug_test.csv",
            file_path=test_file_path,
            file_size=os.path.getsize(test_file_path),
            file_type=".csv",
            status=FileStatus.UPLOADING
        )
        
        print(f"   FileInfo: {file_info.dict()}")
        
        # 3. 检查任务是否已注册
        print("3. 检查任务注册...")
        registered_tasks = [name for name in celery_app.tasks.keys() if not name.startswith('celery.')]
        print(f"   已注册任务: {registered_tasks}")
        
        if 'worker.tasks.vectorize_file' not in celery_app.tasks:
            print("❌ worker.tasks.vectorize_file 任务未注册!")
            return False
        
        # 4. 提交向量化任务
        print("4. 提交向量化任务...")
        # 不指定队列，让任务路由自动处理
        task_result = celery_app.send_task(
            'worker.tasks.vectorize_file',
            args=[file_code, test_file_path, file_info.model_dump()]
        )
        
        print(f"   任务ID: {task_result.id}")
        print(f"   任务状态: {task_result.state}")
        
        # 5. 监控任务进度
        print("5. 监控任务进度...")
        start_time = time.time()
        timeout = 60  # 1分钟超时
        last_state = None
        last_info = None
        
        while not task_result.ready() and time.time() - start_time < timeout:
            current_state = task_result.state
            current_info = task_result.info
            
            # 只在状态或信息变化时输出
            if current_state != last_state or current_info != last_info:
                print(f"   [{datetime.now().strftime('%H:%M:%S')}] 状态: {current_state}")
                if current_info:
                    if isinstance(current_info, dict):
                        stage = current_info.get('stage', '未知')
                        progress = current_info.get('progress', 0)
                        message = current_info.get('message', '')
                        print(f"   [{datetime.now().strftime('%H:%M:%S')}] 进度: {progress}% - {stage}")
                        if message:
                            print(f"   [{datetime.now().strftime('%H:%M:%S')}] 消息: {message}")
                    else:
                        print(f"   [{datetime.now().strftime('%H:%M:%S')}] 信息: {current_info}")
                
                last_state = current_state
                last_info = current_info
            
            time.sleep(2)
        
        # 6. 检查最终结果
        print("6. 检查最终结果...")
        if task_result.ready():
            if task_result.successful():
                result = task_result.get()
                print(f"✅ 任务成功完成!")
                print(f"   结果: {result}")
                return True
            else:
                error = task_result.result
                print(f"❌ 任务失败!")
                print(f"   错误: {error}")
                print(f"   错误类型: {type(error)}")
                if hasattr(error, '__traceback__'):
                    print(f"   堆栈跟踪: {''.join(traceback.format_tb(error.__traceback__))}")
                return False
        else:
            print(f"⏰ 任务超时 (超过 {timeout} 秒)")
            print(f"   最终状态: {task_result.state}")
            print(f"   最终信息: {task_result.info}")
            
            # 尝试撤销任务
            try:
                celery_app.control.revoke(task_result.id, terminate=True)
                print(f"   已撤销任务: {task_result.id}")
            except Exception as e:
                print(f"   撤销任务失败: {e}")
            
            return False
    
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        print(f"   异常类型: {type(e)}")
        print(f"   堆栈跟踪:")
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        try:
            if os.path.exists(test_file_path):
                os.unlink(test_file_path)
                print(f"🧹 已清理测试文件: {test_file_path}")
        except Exception as e:
            print(f"⚠️ 清理测试文件失败: {e}")


def check_worker_logs():
    """检查worker日志"""
    print("\n📋 检查worker进程输出...")
    
    # 查找worker进程
    import subprocess
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        worker_lines = [line for line in result.stdout.split('\n') if 'celery' in line and 'worker' in line]
        
        print(f"找到 {len(worker_lines)} 个worker进程:")
        for line in worker_lines:
            print(f"  {line}")
    except Exception as e:
        print(f"检查进程失败: {e}")


def main():
    """主函数"""
    print("🔧 向量化任务调试工具")
    print("=" * 50)
    
    # 检查worker状态
    check_worker_logs()
    
    # 测试向量化任务
    success = test_vectorization_task()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 向量化任务调试完成 - 任务正常")
    else:
        print("❌ 向量化任务调试完成 - 发现问题")
        print("\n💡 可能的解决方案:")
        print("1. 检查worker日志文件中的详细错误信息")
        print("2. 确认BGE-M3模型是否正确加载")
        print("3. 检查ChromaDB服务是否正常运行")
        print("4. 验证文件路径和权限")
        print("5. 重启worker服务")


if __name__ == "__main__":
    main()
