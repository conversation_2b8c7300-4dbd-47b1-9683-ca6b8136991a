#!/bin/bash

# Deep Risk RAG 异步架构依赖安装脚本

set -e

echo "🚀 开始安装异步架构依赖..."

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ Python未找到，请先安装Python"
    exit 1
fi

# 检查pip
if ! command -v pip &> /dev/null; then
    echo "❌ pip未找到，请先安装pip"
    exit 1
fi

echo "📦 当前Python版本:"
python --version

echo "📦 当前pip版本:"
pip --version

# 升级pip
echo "⬆️  升级pip..."
pip install --upgrade pip

# 安装新的依赖
echo "📥 安装异步架构依赖..."

# 安装Celery相关依赖
echo "  - 安装Celery..."
pip install celery==5.4.0

echo "  - 安装Redis客户端..."
pip install redis==5.2.1

echo "  - 安装Flower监控工具..."
pip install flower==2.0.1

# 安装Web服务依赖（如果还没有）
echo "  - 检查并安装Web服务依赖..."
pip install fastapi==0.115.6
pip install uvicorn[standard]==0.32.1
pip install python-multipart==0.0.20
pip install aiofiles==24.1.0

# 安装所有依赖
echo "📋 从requirements.txt安装所有依赖..."
pip install -r requirements.txt

echo "✅ 依赖安装完成!"

# 验证安装
echo "🔍 验证关键依赖安装..."

python -c "import celery; print(f'Celery版本: {celery.__version__}')" || echo "❌ Celery安装失败"
python -c "import redis; print(f'Redis客户端版本: {redis.__version__}')" || echo "❌ Redis客户端安装失败"
python -c "import fastapi; print(f'FastAPI版本: {fastapi.__version__}')" || echo "❌ FastAPI安装失败"

echo "🎉 异步架构依赖安装完成!"
echo ""
echo "📝 接下来的步骤:"
echo "1. 确保Redis服务器正在运行"
echo "2. 启动Celery Worker: celery -A shared.celery_config worker --loglevel=info"
echo "3. 启动Web服务"
echo "4. (可选) 启动Flower监控: celery -A shared.celery_config flower"
