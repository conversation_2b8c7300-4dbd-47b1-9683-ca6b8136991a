"""
配置文件 - Deep Risk RAG 系统
"""

from pathlib import Path
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """系统配置类"""

    # 项目路径
    PROJECT_ROOT: Path = Path(__file__).parent
    DATA_DIR: Path = PROJECT_ROOT / "data"
    DOCUMENTS_DIR: Path = DATA_DIR / "documents"
    CACHE_DIR: Path = PROJECT_ROOT / "cache"
    MODELS_CACHE_DIR: Path = CACHE_DIR / "models"

    # LLM 提供商配置
    LLM_PROVIDER: str = "openai"  # 可选: deepseek, openai

    # DeepSeek API 配置
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
    DEEPSEEK_MODEL: str = "deepseek-reasoner"

    # OpenAI API 配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_BASE_URL: Optional[str] = None  # 可选，用于兼容OpenAI格式的其他服务
    OPENAI_MODEL: Optional[str] = None

    # 嵌入模型配置
    EMBEDDING_MODEL_NAME: str = "/Users/<USER>/VsCodeProjects/deep-risk-rag/models/bge-m3-safetensors-only"
    EMBEDDING_MODEL_CACHE_DIR: Optional[str] = None
    EMBEDDING_BATCH_SIZE: int = 16
    EMBEDDING_MAX_LENGTH: int = 8192  # BGE-M3 模型支持最大8192 tokens

    # 文本处理策略配置
    TEXT_PROCESSING_STRATEGY: str = (
        "smart_truncate"  # smart_truncate, head_tail, sliding_window
    )
    ENABLE_TEXT_SPLITTING: bool = True  # 是否将长文本分割为多个嵌入
    TEXT_SPLIT_OVERLAP: int = 200  # 文本分割时的重叠字符数（增加以适应更长序列）
    MAX_TEXT_CHARS_PER_EMBEDDING: int = 32768  # 每个嵌入的最大字符数 (8192 tokens * 4)

    # 向量数据库配置
    VECTOR_DB_TYPE: str = "chroma"  # chroma, faiss
    CHROMA_PERSIST_DIR: str = "./cache/chroma_db"
    CHROMA_COLLECTION_NAME: str = "deep_risk_documents"

    # 文档处理配置
    CHUNK_SIZE: int = 4000  # 增加块大小以利用BGE-M3的长序列能力
    CHUNK_OVERLAP: int = 400  # 相应增加重叠大小
    MAX_DOCUMENT_SIZE_MB: int = 50

    # 检索配置
    RETRIEVAL_TOP_K: int = 5
    SIMILARITY_THRESHOLD: float = 0.7

    # RAG 配置
    ENABLE_DOCUMENT_RETRIEVAL: bool = True  # 是否启用文档检索（关闭后变为纯LLM对话）
    MAX_CONTEXT_LENGTH: int = (
        64000  # 最大上下文长度（DeepSeek支持64K+，32K约8K tokens）
    )
    TEMPERATURE: float = 0.1
    MAX_TOKENS: int = 64000  # 最大生成token数（DeepSeek支持64K+，32K约8K tokens）

    # 风险预测配置
    RISK_PREDICTION_ENABLED: bool = True  # 是否启用风险预测功能
    FILE_CODED_VECTOR_STORE_DIR: str = (
        "./cache/file_coded_chroma"  # 文件编码向量存储目录
    )
    RISK_ANALYSIS_TIMEOUT: int = 300  # 风险分析超时时间（秒）
    MAX_EXCEL_FILE_SIZE_MB: int = 10  # Excel文件最大大小限制（MB）
    ENABLE_STREAMING_OUTPUT: bool = True  # 是否启用流式输出（改善交互体验）

    # 指令模板配置
    RISK_ANALYSIS_PROMPT_FILE: str = "prompts/risk_analysis/origin.md"  # 风控分析指令模板文件路径

    # 系统配置
    LOG_LEVEL: str = "INFO"
    ENABLE_CACHE: bool = True
    CACHE_TTL_SECONDS: int = 3600

    # ChromaDB 配置
    ANONYMIZED_TELEMETRY: bool = False

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 全局配置实例
settings = Settings()


# 确保必要的目录存在
def ensure_directories():
    """确保所有必要的目录存在"""
    directories = [
        settings.DATA_DIR,
        settings.DOCUMENTS_DIR,
        settings.CACHE_DIR,
        settings.MODELS_CACHE_DIR,
        Path(settings.CHROMA_PERSIST_DIR).parent,
    ]

    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

    print("✅ 项目目录结构已创建")
    print(f"📁 数据目录: {settings.DOCUMENTS_DIR}")
    print(f"📁 缓存目录: {settings.CACHE_DIR}")
    print(f"📁 模型缓存: {settings.MODELS_CACHE_DIR}")


def validate_bge_m3_config():
    """验证BGE-M3相关配置的合理性"""
    errors = []
    warnings = []

    # 检查模型名称
    if settings.EMBEDDING_MODEL_NAME != "BAAI/bge-m3":
        warnings.append(f"模型名称不是标准的BGE-M3: {settings.EMBEDDING_MODEL_NAME}")

    # 检查序列长度
    if settings.EMBEDDING_MAX_LENGTH > 8192:
        errors.append(
            f"BGE-M3最大序列长度不能超过8192，当前设置: {settings.EMBEDDING_MAX_LENGTH}"
        )
    elif settings.EMBEDDING_MAX_LENGTH < 512:
        warnings.append(f"序列长度过小可能影响性能: {settings.EMBEDDING_MAX_LENGTH}")

    # 检查批处理大小
    if settings.EMBEDDING_BATCH_SIZE > 64:
        warnings.append(
            f"批处理大小过大可能导致内存不足: {settings.EMBEDDING_BATCH_SIZE}"
        )
    elif settings.EMBEDDING_BATCH_SIZE < 1:
        errors.append(f"批处理大小必须大于0: {settings.EMBEDDING_BATCH_SIZE}")

    # 检查文档块大小
    if settings.CHUNK_SIZE > settings.EMBEDDING_MAX_LENGTH * 4:
        warnings.append(f"文档块大小可能超过模型处理能力: {settings.CHUNK_SIZE}")

    # 输出验证结果
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"   • {error}")
        return False

    if warnings:
        print("⚠️  配置警告:")
        for warning in warnings:
            print(f"   • {warning}")

    if not errors and not warnings:
        print("✅ BGE-M3配置验证通过")

    return True


if __name__ == "__main__":
    ensure_directories()
    validate_bge_m3_config()
