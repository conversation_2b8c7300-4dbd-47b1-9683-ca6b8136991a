#!/usr/bin/env python3
"""
分析服务命令行入口
保持与原有risk_prediction_main.py的兼容性
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from services.analysis_service.cli.interactive import InteractiveAnalyzer
from services.analysis_service.core.risk_analyzer import RiskAnalyzer
from shared.utils.logger import setup_logging, get_logger

# 设置日志
setup_logging(level="INFO", service_name="analysis-cli")
logger = get_logger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Deep Risk RAG 风险分析系统 (微服务版本)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 交互式模式
  python cli_main.py --interactive
  
  # 分析指定文件
  python cli_main.py --analyze file_code_123
  
  # 列出可用文件
  python cli_main.py --list-files
  
  # 查看分析结果
  python cli_main.py --result analysis_id_456
        """,
    )

    parser.add_argument(
        "--interactive", "-i", action="store_true", help="启动交互式界面"
    )

    parser.add_argument(
        "--analyze", "-a", metavar="FILE_CODE", help="分析指定的文件编码"
    )

    parser.add_argument(
        "--list-files", "-l", action="store_true", help="列出所有可用文件"
    )

    parser.add_argument(
        "--result", "-r", metavar="ANALYSIS_ID", help="查看指定分析结果"
    )

    parser.add_argument("--health", action="store_true", help="检查系统健康状态")

    args = parser.parse_args()

    try:
        if args.interactive:
            # 启动交互式界面
            analyzer = InteractiveAnalyzer()
            analyzer.run()

        elif args.analyze:
            # 分析指定文件
            analyze_file(args.analyze)

        elif args.list_files:
            # 列出可用文件
            list_files()

        elif args.result:
            # 查看分析结果
            show_result(args.result)

        elif args.health:
            # 健康检查
            check_health()

        else:
            # 默认启动交互式界面
            print("🚀 启动交互式界面...")
            analyzer = InteractiveAnalyzer()
            analyzer.run()

    except KeyboardInterrupt:
        print("\n👋 用户中断，退出系统")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"❌ 执行失败: {e}")
        sys.exit(1)


def analyze_file(file_code: str):
    """分析指定文件"""
    try:
        print(f"🔍 开始分析文件: {file_code}")

        risk_analyzer = RiskAnalyzer()
        result = risk_analyzer.analyze_risk(file_code)

        print("\n" + "=" * 60)
        print("🎯 风险分析结果")
        print("=" * 60)

        print(f"📋 分析ID: {result.analysis_id}")
        print(f"📁 文件编码: {result.file_code}")
        print(f"📊 状态: {result.status}")

        if result.default_probability is not None:
            print(f"⚠️  违约概率: {result.default_probability:.2%}")

        if result.risk_level:
            print(f"🎯 风险等级: {result.risk_level}")

        if result.analysis_summary:
            print(f"\n📝 分析摘要:")
            print("-" * 40)
            print(result.analysis_summary)

        print("=" * 60)

    except Exception as e:
        logger.error(f"分析文件失败: {e}")
        print(f"❌ 分析失败: {e}")


def list_files():
    """列出可用文件"""
    try:
        from services.analysis_service.core.chroma_retriever import ChromaRetriever

        print("📁 正在查询可用文件...")

        retriever = ChromaRetriever()
        file_codes = retriever.list_available_files()

        if not file_codes:
            print("📭 暂无可用文件")
            return

        print(f"\n📊 找到 {len(file_codes)} 个可用文件:")
        print("-" * 60)

        for i, file_code in enumerate(file_codes, 1):
            file_info = retriever.get_file_info(file_code)
            doc_count = file_info.get("document_count", "未知") if file_info else "未知"
            print(f"{i:2d}. {file_code} (文档数: {doc_count})")

        print("-" * 60)

    except Exception as e:
        logger.error(f"列出文件失败: {e}")
        print(f"❌ 获取文件列表失败: {e}")


def show_result(analysis_id: str):
    """显示分析结果"""
    try:
        print(f"📊 正在查询分析结果: {analysis_id}")

        risk_analyzer = RiskAnalyzer()
        result = risk_analyzer.get_analysis_result(analysis_id)

        if not result:
            print(f"❌ 未找到分析结果: {analysis_id}")
            return

        print("\n" + "=" * 60)
        print("🎯 分析结果详情")
        print("=" * 60)

        print(f"📋 分析ID: {result.analysis_id}")
        print(f"📁 文件编码: {result.file_code}")
        print(f"📊 状态: {result.status}")
        print(f"📅 创建时间: {result.created_at}")

        if result.default_probability is not None:
            print(f"⚠️  违约概率: {result.default_probability:.2%}")

        if result.risk_level:
            print(f"🎯 风险等级: {result.risk_level}")

        if result.key_factors:
            print(f"\n🔑 关键风险因素:")
            for i, factor in enumerate(result.key_factors, 1):
                print(f"   {i}. {factor}")

        if result.analysis_summary:
            print(f"\n📝 分析摘要:")
            print("-" * 40)
            print(result.analysis_summary)

        print("=" * 60)

    except Exception as e:
        logger.error(f"查询分析结果失败: {e}")
        print(f"❌ 查询失败: {e}")


def check_health():
    """检查系统健康状态"""
    try:
        print("🔧 正在检查系统状态...")

        risk_analyzer = RiskAnalyzer()
        checks = risk_analyzer.health_check()

        print("\n📊 系统组件状态:")
        print("-" * 40)

        for component, status in checks.items():
            status_emoji = "✅" if status else "❌"
            status_text = "正常" if status else "异常"
            print(f"{status_emoji} {component}: {status_text}")

        overall_status = all(checks.values())
        overall_emoji = "✅" if overall_status else "❌"
        overall_text = "健康" if overall_status else "异常"

        print("-" * 40)
        print(f"{overall_emoji} 整体状态: {overall_text}")

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        print(f"❌ 健康检查失败: {e}")


if __name__ == "__main__":
    main()
