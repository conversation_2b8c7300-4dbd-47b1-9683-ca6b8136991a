"""
BGE-M3嵌入服务配置
"""

import os
from pathlib import Path
from typing import Optional
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings

# 添加项目根目录到Python路径
import sys
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from shared.system_compatibility import get_optimal_device, should_force_cpu


class EmbeddingServiceConfig(BaseSettings):
    """嵌入服务配置"""

    # 服务基本信息
    service_name: str = "embedding-service"
    service_version: str = "1.0.0"
    host: str = os.getenv("HOST", "0.0.0.0")
    port: int = int(os.getenv("PORT", "8004"))
    debug: bool = os.getenv("DEBUG", "false").lower() == "true"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")

    # BGE-M3模型配置
    model_name: str = os.getenv("MODEL_NAME", str(project_root / "models" / "bge-m3-safetensors-only"))
    model_cache_dir: Optional[str] = os.getenv("MODEL_CACHE_DIR", "./models")

    # 设备配置
    _optimal_device: str = get_optimal_device()
    device: str = os.getenv("DEVICE", _optimal_device)
    force_cpu: bool = should_force_cpu()

    # 嵌入配置
    batch_size: int = int(os.getenv("BATCH_SIZE", "32"))
    max_length: int = int(os.getenv("MAX_LENGTH", "8192"))
    normalize_embeddings: bool = True
    
    # 性能配置
    max_concurrent_requests: int = int(os.getenv("MAX_CONCURRENT_REQUESTS", "10"))
    request_timeout: int = int(os.getenv("REQUEST_TIMEOUT", "300"))  # 5分钟
    
    # 内存管理配置
    enable_memory_monitoring: bool = os.getenv("ENABLE_MEMORY_MONITORING", "true").lower() == "true"
    auto_cleanup: bool = os.getenv("AUTO_CLEANUP", "true").lower() == "true"
    memory_cleanup_threshold: float = float(os.getenv("MEMORY_CLEANUP_THRESHOLD", "0.8"))  # 80%
    
    # 缓存配置
    enable_cache: bool = os.getenv("ENABLE_CACHE", "true").lower() == "true"
    cache_ttl: int = int(os.getenv("CACHE_TTL", "3600"))  # 1小时
    max_cache_size: int = int(os.getenv("MAX_CACHE_SIZE", "1000"))
    
    # API配置
    api_prefix: str = "/api/v1"
    enable_docs: bool = os.getenv("ENABLE_DOCS", "true").lower() == "true"
    
    # 健康检查配置
    health_check_interval: int = int(os.getenv("HEALTH_CHECK_INTERVAL", "30"))  # 30秒
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# 全局配置实例
config = EmbeddingServiceConfig()
