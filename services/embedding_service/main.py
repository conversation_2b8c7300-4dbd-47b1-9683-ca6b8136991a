"""
BGE-M3嵌入服务主应用
提供独立的文本嵌入计算服务
"""

import sys
from pathlib import Path
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from shared.utils.logger import setup_logging, get_logger
from services.embedding_service.config import config
from services.embedding_service.api import health, embed
from services.embedding_service.core.embedding_manager import EmbeddingManager

# 设置日志
setup_logging(level=config.log_level, service_name=config.service_name)
logger = get_logger(__name__)

# 全局嵌入管理器
embedding_manager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理器"""
    global embedding_manager

    # 启动时执行
    logger.info("=" * 60)
    logger.info(f"🚀 启动 {config.service_name} v{config.service_version}")
    logger.info("=" * 60)

    # 服务配置信息
    logger.info(f"📡 服务地址: {config.host}:{config.port}")
    logger.info(f"🐛 调试模式: {config.debug}")
    logger.info(f"📝 日志级别: {config.log_level}")

    # 模型配置信息
    logger.info(f"🤖 模型路径: {config.model_name}")
    logger.info(f"🔧 设备: {config.device}")
    logger.info(f"📊 批处理大小: {config.batch_size}")
    logger.info(f"📏 最大长度: {config.max_length}")

    # 性能配置信息
    logger.info(f"🔄 最大并发请求: {config.max_concurrent_requests}")
    logger.info(f"⏱️  请求超时: {config.request_timeout}秒")
    logger.info(f"💾 启用缓存: {config.enable_cache}")
    logger.info(f"🧹 自动清理: {config.auto_cleanup}")

    try:
        # 初始化嵌入管理器
        logger.info("🔄 初始化嵌入管理器...")
        embedding_manager = EmbeddingManager()
        await embedding_manager.initialize()

        logger.info("✅ 嵌入服务启动完成")
        logger.info("=" * 60)

    except Exception as e:
        logger.error(f"❌ 嵌入服务启动失败: {e}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("=" * 60)
    logger.info(f"🛑 关闭 {config.service_name}")
    
    try:
        if embedding_manager:
            logger.info("🧹 清理嵌入管理器...")
            await embedding_manager.cleanup()
            
        logger.info("✅ 资源清理完成")
        
    except Exception as e:
        logger.error(f"❌ 资源清理失败: {e}")
    
    logger.info("✅ 服务关闭完成")
    logger.info("=" * 60)


# 创建FastAPI应用
app = FastAPI(
    title="BGE-M3 Embedding Service",
    description="独立的BGE-M3文本嵌入计算服务",
    version=config.service_version,
    docs_url="/docs" if config.enable_docs else None,
    redoc_url="/redoc" if config.enable_docs else None,
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health.router, prefix=config.api_prefix)
app.include_router(embed.router, prefix=config.api_prefix)


@app.get("/")
async def root():
    """根路径 - 返回服务信息"""
    return {
        "service": config.service_name,
        "version": config.service_version,
        "description": "BGE-M3 Embedding Service",
        "status": "running",
        "features": [
            "BGE-M3文本嵌入",
            "批量处理支持",
            "内存优化管理",
            "缓存机制",
            "健康检查",
            "性能监控",
        ],
        "endpoints": {
            "health": f"{config.api_prefix}/health",
            "embed_documents": f"{config.api_prefix}/embed/documents",
            "embed_query": f"{config.api_prefix}/embed/query",
            "model_info": f"{config.api_prefix}/model/info",
        }
    }


def get_embedding_manager() -> EmbeddingManager:
    """获取全局嵌入管理器"""
    return embedding_manager


if __name__ == "__main__":
    uvicorn.run(
        app,
        host=config.host,
        port=config.port,
        reload=config.debug,
        log_level=config.log_level.lower(),
    )
