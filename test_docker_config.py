#!/usr/bin/env python3
"""
测试Docker配置
"""

import yaml
from pathlib import Path

def test_docker_compose():
    """测试Docker Compose配置"""
    print("测试Docker Compose配置...")
    
    compose_file = Path("docker/docker-compose.yml")
    if not compose_file.exists():
        print("❌ docker-compose.yml文件不存在")
        return False
    
    try:
        with open(compose_file, 'r', encoding='utf-8') as f:
            compose_config = yaml.safe_load(f)
        
        services = compose_config.get('services', {})
        
        # 检查ChromaDB服务
        chromadb = services.get('chromadb', {})
        print(f"ChromaDB挂载路径: {chromadb.get('volumes', [])}")
        
        # 检查vector-service
        vector_service = services.get('vector-service', {})
        vector_env = vector_service.get('environment', [])
        print(f"Vector Service环境变量: {vector_env}")
        
        # 检查analysis-service
        analysis_service = services.get('analysis-service', {})
        analysis_env = analysis_service.get('environment', [])
        print(f"Analysis Service环境变量: {analysis_env}")
        
        # 验证关键配置
        checks = []
        
        # 检查ChromaDB挂载路径
        chroma_volumes = chromadb.get('volumes', [])
        if any('chroma_db' in vol for vol in chroma_volumes):
            checks.append("✅ ChromaDB挂载路径正确")
        else:
            checks.append("❌ ChromaDB挂载路径错误")
        
        # 检查vector-service配置
        if any('CHROMA_PERSIST_DIR' in env for env in vector_env):
            checks.append("✅ Vector Service ChromaDB配置正确")
        else:
            checks.append("❌ Vector Service ChromaDB配置缺失")
        
        # 检查analysis-service配置
        if any('CHROMA_HOST' in env for env in analysis_env):
            checks.append("✅ Analysis Service ChromaDB连接配置正确")
        else:
            checks.append("❌ Analysis Service ChromaDB连接配置缺失")
        
        for check in checks:
            print(check)
        
        success = all("✅" in check for check in checks)
        if success:
            print("🎉 Docker配置验证通过！")
        else:
            print("❌ Docker配置需要修正")
        
        return success
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def test_architecture_summary():
    """输出架构总结"""
    print("\n" + "="*60)
    print("架构修正总结")
    print("="*60)
    
    print("\n📋 修正内容:")
    print("1. ✅ vector_service改为本地ChromaDB模式")
    print("2. ✅ 使用langchain_chroma.Chroma进行本地持久化")
    print("3. ✅ ChromaDB服务挂载vector_service数据目录")
    print("4. ✅ analysis_service作为ChromaDB客户端连接")
    print("5. ✅ 移除了远程ChromaDB客户端代码")
    
    print("\n🏗️ 数据流:")
    print("vector_service (本地ChromaDB) → ChromaDB服务 → analysis_service (客户端)")
    
    print("\n📁 目录结构:")
    print("./cache/chroma_db/")
    print("├── file_code_1/")
    print("│   └── chroma.sqlite3")
    print("├── file_code_2/")
    print("│   └── chroma.sqlite3")
    print("└── ...")
    
    print("\n🔧 服务配置:")
    print("- vector_service: CHROMA_PERSIST_DIR=/app/cache/chroma_db")
    print("- chromadb: 挂载 ../cache/chroma_db:/chroma/chroma")
    print("- analysis_service: CHROMA_HOST=chromadb, CHROMA_PORT=8000")

if __name__ == "__main__":
    success = test_docker_compose()
    test_architecture_summary()
    
    if success:
        print("\n🎉 架构修正完成！可以开始部署测试。")
    else:
        print("\n❌ 需要进一步修正配置。")
