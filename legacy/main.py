#!/usr/bin/env python3
"""
Deep Risk RAG 系统主程序
基于 LangChain + DeepSeek + gte-large 的本地知识库问答系统
"""

# import os
import sys
from pathlib import Path
# from typing import Optional
import logging

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.markdown import Markdown

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from config import settings, ensure_directories
    from src.embeddings import GTELargeEmbeddings
    from src.vector_store import ChromaVectorStore, VectorStoreManager
    from src.rag_chain import DeepSeekRAGChain
    # 注意：风险预测功能已独立到 risk_prediction_main.py
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)

# 初始化控制台
console = Console()

# 设置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL, logging.INFO),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class RAGSystem:
    """RAG 系统主类"""

    def __init__(self):
        """初始化系统"""
        self.embeddings = None
        self.vector_store = None
        self.vector_manager = None
        self.rag_chain = None
        self.initialized = False

    def initialize(self) -> bool:
        """初始化系统组件"""
        try:
            console.print("\n🚀 正在初始化 Deep Risk RAG 系统...", style="blue")

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                # 确保目录存在
                task1 = progress.add_task("创建项目目录...", total=None)
                ensure_directories()
                progress.update(task1, completed=True)

                # 初始化嵌入服务
                task2 = progress.add_task("加载嵌入模型...", total=None)
                self.embeddings = GTELargeEmbeddings()
                progress.update(task2, completed=True)

                # 初始化向量存储
                task3 = progress.add_task("初始化向量数据库...", total=None)
                self.vector_store = ChromaVectorStore(embeddings=self.embeddings)
                self.vector_manager = VectorStoreManager(self.vector_store)
                progress.update(task3, completed=True)

                # 初始化 RAG 链
                task4 = progress.add_task("初始化问答链...", total=None)
                self.rag_chain = DeepSeekRAGChain(vector_store=self.vector_store)
                progress.update(task4, completed=True)

            self.initialized = True
            console.print("✅ 系统初始化完成！", style="green")
            return True

        except Exception as e:
            console.print(f"❌ 系统初始化失败: {e}", style="red")
            logger.error(f"系统初始化失败: {e}")
            return False

    def show_system_info(self):
        """显示系统信息"""
        if not self.initialized:
            console.print("❌ 系统未初始化", style="red")
            return

        # 获取系统信息
        embedding_info = self.embeddings.get_model_info()
        vector_info = self.vector_store.get_collection_info()
        chain_info = self.rag_chain.get_chain_info()

        # 创建信息表格
        table = Table(title="🤖 Deep Risk RAG 系统信息")
        table.add_column("组件", style="cyan", no_wrap=True)
        table.add_column("信息", style="white")

        # 嵌入模型信息
        table.add_row(
            "嵌入模型",
            f"{embedding_info.get('model_name', 'N/A')}\n"
            f"维度: {embedding_info.get('embedding_dimension', 'N/A')}\n"
            f"设备: {embedding_info.get('device', 'N/A')}\n"
            f"缓存: {embedding_info.get('cache_size', 0)} 条",
        )

        # 向量数据库信息
        table.add_row(
            "向量数据库",
            f"类型: Chroma\n"
            f"集合: {vector_info.get('collection_name', 'N/A')}\n"
            f"文档数: {vector_info.get('document_count', 0)}\n"
            f"目录: {vector_info.get('persist_directory', 'N/A')}",
        )

        # LLM 信息
        table.add_row(
            "语言模型",
            f"模型: {chain_info.get('model_name', 'N/A')}\n"
            f"温度: {chain_info.get('temperature', 'N/A')}\n"
            f"最大长度: {chain_info.get('max_tokens', 'N/A')}\n"
            f"检索数量: {chain_info.get('retrieval_top_k', 'N/A')}",
        )

        console.print(table)

    def index_documents(self, directory_path: str, clear_existing: bool = False):
        """索引文档"""
        if not self.initialized:
            console.print("❌ 系统未初始化", style="red")
            return

        directory_path = Path(directory_path)
        if not directory_path.exists():
            console.print(f"❌ 目录不存在: {directory_path}", style="red")
            return

        console.print(f"\n📚 正在索引文档目录: {directory_path}", style="blue")

        if clear_existing:
            if Confirm.ask("⚠️  确定要清空现有的向量数据库吗？"):
                self.vector_store.clear_collection()
                console.print("✅ 向量数据库已清空", style="green")
            else:
                clear_existing = False

        # 执行索引
        result = self.vector_manager.index_documents_from_directory(
            directory_path=directory_path, clear_existing=clear_existing
        )

        if result.get("success"):
            console.print(f"✅ 文档索引完成！", style="green")
            console.print(f"原始文档: {result.get('original_documents', 0)}")
            console.print(f"分割块数: {result.get('split_documents', 0)}")
            console.print(f"索引文档: {result.get('indexed_documents', 0)}")
        else:
            console.print(
                f"❌ 文档索引失败: {result.get('error', '未知错误')}", style="red"
            )

    def interactive_chat(self):
        """交互式问答"""
        if not self.initialized:
            console.print("❌ 系统未初始化", style="red")
            return

        # 检查是否有文档
        vector_info = self.vector_store.get_collection_info()
        doc_count = vector_info.get("document_count", 0)

        if doc_count == 0:
            console.print("⚠️  向量数据库中没有文档，请先索引一些文档。", style="yellow")
            if Confirm.ask("是否现在索引文档？"):
                docs_dir = Prompt.ask(
                    "请输入文档目录路径", default=str(settings.DOCUMENTS_DIR)
                )
                self.index_documents(docs_dir)
                return

        # 显示欢迎信息
        welcome_panel = Panel.fit(
            f"""
[bold blue]🤖 Deep Risk RAG 智能问答系统[/bold blue]

当前知识库包含 [bold green]{doc_count}[/bold green] 个文档块

[bold]使用说明:[/bold]
• 输入您的问题，系统会基于知识库内容回答
• 输入 'quit' 或 'exit' 退出
• 输入 'help' 查看帮助
• 输入 'info' 查看系统信息

开始提问吧！
            """,
            title="💬 智能问答",
            border_style="blue",
        )
        console.print(welcome_panel)

        # 开始对话循环
        while True:
            try:
                # 获取用户输入
                question = Prompt.ask("\n[bold blue]您的问题[/bold blue]", default="")

                if not question.strip():
                    continue

                # 处理特殊命令
                if question.lower() in ["quit", "exit", "退出"]:
                    console.print("👋 再见！", style="green")
                    break
                elif question.lower() in ["help", "帮助"]:
                    self._show_help()
                    continue
                elif question.lower() in ["info", "信息"]:
                    self.show_system_info()
                    continue

                # 处理问题
                console.print("\n🤔 正在思考...", style="yellow")

                result = self.rag_chain.ask(question, include_sources=True)

                if result.get("success"):
                    # 显示答案
                    answer_panel = Panel(
                        Markdown(result["answer"]),
                        title="🤖 AI 回答",
                        border_style="green",
                    )
                    console.print(answer_panel)

                    # 显示来源（如果有）
                    sources = result.get("sources", [])
                    if sources:
                        console.print(
                            f"\n📚 参考来源 ({len(sources)} 个文档):", style="blue"
                        )
                        for i, source in enumerate(sources[:3], 1):  # 只显示前3个来源
                            console.print(f"  {i}. {source['file_name']}")
                            console.print(f"     {source['content_preview'][:100]}...")
                else:
                    console.print(f"❌ {result.get('answer', '处理失败')}", style="red")

            except KeyboardInterrupt:
                console.print("\n\n👋 再见！", style="green")
                break
            except Exception as e:
                console.print(f"\n❌ 发生错误: {e}", style="red")
                logger.error(f"交互式问答错误: {e}")

    def _show_help(self):
        """显示帮助信息"""
        help_panel = Panel.fit(
            """
[bold blue]📖 帮助信息[/bold blue]

[bold]可用命令:[/bold]
• [cyan]quit/exit[/cyan] - 退出程序
• [cyan]help[/cyan] - 显示此帮助信息
• [cyan]info[/cyan] - 显示系统信息

[bold]使用技巧:[/bold]
• 尽量使用具体、明确的问题
• 可以询问文档中的具体内容
• 系统会基于知识库内容回答，无关内容可能无法回答

[bold]示例问题:[/bold]
• "文档中提到了哪些风险因素？"
• "请总结主要的结论"
• "关于XXX的具体内容是什么？"
            """,
            title="💡 使用帮助",
            border_style="yellow",
        )
        console.print(help_panel)


# CLI 命令
@click.group()
@click.version_option(version="1.0.0")
def cli():
    """Deep Risk RAG 系统 - 基于 LangChain + DeepSeek + gte-large 的智能问答系统"""
    pass


@cli.command()
@click.option("--docs-dir", "-d", default=None, help="文档目录路径")
@click.option("--clear", "-c", is_flag=True, help="清空现有数据")
def index(docs_dir, clear):
    """索引文档到向量数据库"""
    system = RAGSystem()
    if not system.initialize():
        sys.exit(1)

    if docs_dir is None:
        docs_dir = str(settings.DOCUMENTS_DIR)

    system.index_documents(docs_dir, clear_existing=clear)


@cli.command()
def chat():
    """启动交互式问答"""
    system = RAGSystem()
    if not system.initialize():
        sys.exit(1)

    system.interactive_chat()


@cli.command()
def info():
    """显示系统信息"""
    system = RAGSystem()
    if not system.initialize():
        sys.exit(1)

    system.show_system_info()


@cli.command()
@click.argument("question")
def ask(question):
    """提问单个问题"""
    system = RAGSystem()
    if not system.initialize():
        sys.exit(1)

    result = system.rag_chain.ask(question, include_sources=False)

    if result.get("success"):
        console.print(f"\n🤖 {result['answer']}", style="green")
    else:
        console.print(f"❌ {result.get('answer', '处理失败')}", style="red")


# 删除性能监控命令，因为已移除性能监控模块


if __name__ == "__main__":
    # 默认启动交互式问答
    if len(sys.argv) == 1:
        system = RAGSystem()
        if system.initialize():
            system.interactive_chat()
    else:
        cli()
