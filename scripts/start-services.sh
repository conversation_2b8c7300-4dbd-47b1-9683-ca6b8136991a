#!/bin/bash

# Deep Risk RAG 服务启动脚本

set -e

echo "🚀 启动 Deep Risk RAG 微服务系统..."

# 进入docker目录
cd docker

# 检查环境变量
if [ ! -f ../.env ]; then
    echo "❌ 环境变量文件 .env 不存在，请先运行 setup.sh"
    exit 1
fi

# 加载环境变量
export $(cat ../.env | grep -v '^#' | xargs)

# 检查必要的环境变量
if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "⚠️  警告: DEEPSEEK_API_KEY 未设置，分析服务可能无法正常工作"
fi

# 启动服务
echo "🐳 启动Docker服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 健康检查
echo "🏥 执行健康检查..."

# 检查ChromaDB
echo "  - 检查 ChromaDB..."
if curl -s http://localhost:8001/api/v1/heartbeat > /dev/null; then
    echo "    ✅ ChromaDB 运行正常"
else
    echo "    ❌ ChromaDB 未响应"
fi

# 检查向量服务
echo "  - 检查向量服务..."
sleep 5  # 给向量服务更多启动时间
if curl -s http://localhost:8002/health > /dev/null; then
    echo "    ✅ 向量服务运行正常"
else
    echo "    ❌ 向量服务未响应"
fi

# 检查分析服务
echo "  - 检查分析服务..."
if curl -s http://localhost:8003/health > /dev/null; then
    echo "    ✅ 分析服务运行正常"
else
    echo "    ❌ 分析服务未响应"
fi

echo ""
echo "🎉 服务启动完成！"
echo ""
echo "📖 服务访问地址："
echo "  - ChromaDB:    http://localhost:8001"
echo "  - 向量服务:    http://localhost:8002"
echo "  - 分析服务:    http://localhost:8003"
echo ""
echo "📚 API文档："
echo "  - 向量服务:    http://localhost:8002/docs"
echo "  - 分析服务:    http://localhost:8003/docs"
echo ""
echo "🔧 管理命令："
echo "  - 查看日志:    docker-compose logs -f [service_name]"
echo "  - 停止服务:    docker-compose down"
echo "  - 重启服务:    docker-compose restart [service_name]"
